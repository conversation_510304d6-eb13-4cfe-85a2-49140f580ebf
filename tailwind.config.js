/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  theme: {
    extend: {
      colors: {
        // Primary Colors
        'primary': '#1a1a1a', // deep-charcoal
        'primary-foreground': '#ffffff', // white
        
        // Secondary Colors
        'secondary': '#4a5568', // sophisticated-gray
        'secondary-foreground': '#ffffff', // white
        
        // Accent Colors
        'accent': '#3182ce', // professional-blue
        'accent-foreground': '#ffffff', // white
        
        // Background Colors
        'background': '#ffffff', // pure-white
        'surface': '#f7fafc', // subtle-off-white
        
        // Text Colors
        'text-primary': '#2d3748', // rich-dark-gray
        'text-secondary': '#718096', // balanced-medium-gray
        
        // Status Colors
        'success': '#38a169', // professional-green
        'success-foreground': '#ffffff', // white
        'warning': '#d69e2e', // sophisticated-amber
        'warning-foreground': '#ffffff', // white
        'error': '#e53e3e', // clear-red
        'error-foreground': '#ffffff', // white
        
        // Border
        'border': 'rgba(0, 0, 0, 0.1)', // subtle-border
      },
      fontFamily: {
        'heading': ['Inter', 'system-ui', 'sans-serif'],
        'body': ['Inter', 'system-ui', 'sans-serif'],
        'caption': ['Inter', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'Consolas', 'monospace'],
      },
      fontWeight: {
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
      },
      borderRadius: {
        'sm': '6px',
        'md': '8px',
        'lg': '12px',
      },
      boxShadow: {
        'card': '0 1px 3px rgba(0, 0, 0, 0.1)',
        'interactive': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'nav': '0 1px 3px rgba(0, 0, 0, 0.1)',
      },
      transitionTimingFunction: {
        'smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      transitionDuration: {
        'fast': '200ms',
        'normal': '300ms',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      zIndex: {
        '100': '100',
        '150': '150',
        '200': '200',
      },
      animation: {
        'shimmer': 'shimmer 2s linear infinite',
      },
      keyframes: {
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('tailwindcss-animate'),
  ],
}