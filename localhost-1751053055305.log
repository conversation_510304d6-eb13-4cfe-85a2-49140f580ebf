rocket-web.js?_cfg=https%3A%2F%2Fportfolio9495back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.5:1 Warning: Received NaN for the `children` attribute. If this is expected, cast the value to a string.
    at div
    at div
    at div
    at div
    at div
    at section
    at SkillsSection (http://localhost:4028/src/pages/about-page/components/SkillsSection.jsx?t=1751069462549:23:47)
    at main
    at div
    at AboutPage (http://localhost:4028/src/pages/about-page/index.jsx?t=1751069462549:37:3)
    at Routes (http://localhost:4028/node_modules/.vite/deps/react-router-dom.js?v=8e8db481:730:5)
    at ErrorBoundary (http://localhost:4028/src/components/ErrorBoundary.jsx:8:5)
    at Router (http://localhost:4028/node_modules/.vite/deps/react-router-dom.js?v=8e8db481:677:15)
    at BrowserRouter (http://localhost:4028/node_modules/.vite/deps/react-router-dom.js?v=8e8db481:1250:5)
    at Routes
    at App
(anonymous) @ rocket-web.js?_cfg=https%3A%2F%2Fportfolio9495back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.5:1
setTimeout
console.error @ rocket-web.js?_cfg=https%3A%2F%2Fportfolio9495back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.5:1
printWarning @ chunk-TAAGB4MZ.js?v=8e8db481:521
error @ chunk-TAAGB4MZ.js?v=8e8db481:505
validateProperty$1 @ chunk-TAAGB4MZ.js?v=8e8db481:3413
warnUnknownProperties @ chunk-TAAGB4MZ.js?v=8e8db481:3459
validateProperties$2 @ chunk-TAAGB4MZ.js?v=8e8db481:3478
validatePropertiesInDevelopment @ chunk-TAAGB4MZ.js?v=8e8db481:7349
setInitialProperties @ chunk-TAAGB4MZ.js?v=8e8db481:7540
finalizeInitialChildren @ chunk-TAAGB4MZ.js?v=8e8db481:8356
completeWork @ chunk-TAAGB4MZ.js?v=8e8db481:16314
completeUnitOfWork @ chunk-TAAGB4MZ.js?v=8e8db481:19252
performUnitOfWork @ chunk-TAAGB4MZ.js?v=8e8db481:19234
workLoopSync @ chunk-TAAGB4MZ.js?v=8e8db481:19165
renderRootSync @ chunk-TAAGB4MZ.js?v=8e8db481:19144
performConcurrentWorkOnRoot @ chunk-TAAGB4MZ.js?v=8e8db481:18706
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
@stagewise_toolbar-react.js?v=8e8db481:3987  GET http://localhost:5747/ping/stagewise net::ERR_CONNECTION_REFUSED
discoverVSCodeWindows @ @stagewise_toolbar-react.js?v=8e8db481:3987
await in discoverVSCodeWindows
discover @ @stagewise_toolbar-react.js?v=8e8db481:4059
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:4087
B$1 @ @stagewise_toolbar-react.js?v=8e8db481:193
j$1 @ @stagewise_toolbar-react.js?v=8e8db481:136
setTimeout
r22 @ @stagewise_toolbar-react.js?v=8e8db481:183
requestAnimationFrame
w$1 @ @stagewise_toolbar-react.js?v=8e8db481:185
c.diffed @ @stagewise_toolbar-react.js?v=8e8db481:154
l$1.diffed @ @stagewise_toolbar-react.js?v=8e8db481:487
O @ chunk-BCKZVBEU.js?v=8e8db481:218
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
E @ chunk-BCKZVBEU.js?v=8e8db481:309
initToolbar @ @stagewise_toolbar-react.js?v=8e8db481:11640
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:11654
commitHookEffectListMount @ chunk-TAAGB4MZ.js?v=8e8db481:16936
commitPassiveMountOnFiber @ chunk-TAAGB4MZ.js?v=8e8db481:18184
commitPassiveMountEffects_complete @ chunk-TAAGB4MZ.js?v=8e8db481:18157
commitPassiveMountEffects_begin @ chunk-TAAGB4MZ.js?v=8e8db481:18147
commitPassiveMountEffects @ chunk-TAAGB4MZ.js?v=8e8db481:18137
flushPassiveEffectsImpl @ chunk-TAAGB4MZ.js?v=8e8db481:19518
flushPassiveEffects @ chunk-TAAGB4MZ.js?v=8e8db481:19475
(anonymous) @ chunk-TAAGB4MZ.js?v=8e8db481:19356
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
@stagewise_toolbar-react.js?v=8e8db481:3987  GET http://localhost:5748/ping/stagewise net::ERR_CONNECTION_REFUSED
discoverVSCodeWindows @ @stagewise_toolbar-react.js?v=8e8db481:3987
await in discoverVSCodeWindows
discover @ @stagewise_toolbar-react.js?v=8e8db481:4059
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:4087
B$1 @ @stagewise_toolbar-react.js?v=8e8db481:193
j$1 @ @stagewise_toolbar-react.js?v=8e8db481:136
setTimeout
r22 @ @stagewise_toolbar-react.js?v=8e8db481:183
requestAnimationFrame
w$1 @ @stagewise_toolbar-react.js?v=8e8db481:185
c.diffed @ @stagewise_toolbar-react.js?v=8e8db481:154
l$1.diffed @ @stagewise_toolbar-react.js?v=8e8db481:487
O @ chunk-BCKZVBEU.js?v=8e8db481:218
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
E @ chunk-BCKZVBEU.js?v=8e8db481:309
initToolbar @ @stagewise_toolbar-react.js?v=8e8db481:11640
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:11654
commitHookEffectListMount @ chunk-TAAGB4MZ.js?v=8e8db481:16936
commitPassiveMountOnFiber @ chunk-TAAGB4MZ.js?v=8e8db481:18184
commitPassiveMountEffects_complete @ chunk-TAAGB4MZ.js?v=8e8db481:18157
commitPassiveMountEffects_begin @ chunk-TAAGB4MZ.js?v=8e8db481:18147
commitPassiveMountEffects @ chunk-TAAGB4MZ.js?v=8e8db481:18137
flushPassiveEffectsImpl @ chunk-TAAGB4MZ.js?v=8e8db481:19518
flushPassiveEffects @ chunk-TAAGB4MZ.js?v=8e8db481:19475
(anonymous) @ chunk-TAAGB4MZ.js?v=8e8db481:19356
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
