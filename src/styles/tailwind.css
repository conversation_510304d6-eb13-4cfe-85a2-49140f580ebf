@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Colors */
    --color-primary: #1a1a1a; /* deep-charcoal */
    --color-primary-foreground: #ffffff; /* white */
    
    /* Secondary Colors */
    --color-secondary: #4a5568; /* sophisticated-gray */
    --color-secondary-foreground: #ffffff; /* white */
    
    /* Accent Colors */
    --color-accent: #3182ce; /* professional-blue */
    --color-accent-foreground: #ffffff; /* white */
    
    /* Background Colors */
    --color-background: #ffffff; /* pure-white */
    --color-surface: #f7fafc; /* subtle-off-white */
    
    /* Text Colors */
    --color-text-primary: #2d3748; /* rich-dark-gray */
    --color-text-secondary: #718096; /* balanced-medium-gray */
    
    /* Status Colors */
    --color-success: #38a169; /* professional-green */
    --color-success-foreground: #ffffff; /* white */
    --color-warning: #d69e2e; /* sophisticated-amber */
    --color-warning-foreground: #ffffff; /* white */
    --color-error: #e53e3e; /* clear-red */
    --color-error-foreground: #ffffff; /* white */
    
    /* Border and Shadow */
    --color-border: rgba(0, 0, 0, 0.1); /* subtle-border */
    --color-shadow-light: rgba(0, 0, 0, 0.1); /* light-shadow */
    --color-shadow-medium: rgba(0, 0, 0, 0.15); /* medium-shadow */
    
    /* Animation */
    --transition-fast: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-text-primary font-body;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
}

@layer components {
  .nav-shadow {
    box-shadow: 0 1px 3px var(--color-shadow-light);
  }
  
  .card-shadow {
    box-shadow: 0 1px 3px var(--color-shadow-light);
  }
  
  .interactive-shadow {
    box-shadow: 0 4px 6px var(--color-shadow-light);
  }
  
  .transition-smooth {
    transition: all var(--transition-fast);
  }
  
  .transition-layout {
    transition: all var(--transition-normal);
  }
}