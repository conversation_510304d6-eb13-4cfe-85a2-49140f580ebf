import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const NavigationIndicator = ({ sections = [] }) => {
  const [activeSection, setActiveSection] = useState('');
  const location = useLocation();

  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '-20% 0px -80% 0px',
      threshold: 0,
    };

    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setActiveSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    sections.forEach((section) => {
      const element = document.getElementById(section.id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => {
      sections.forEach((section) => {
        const element = document.getElementById(section.id);
        if (element) {
          observer.unobserve(element);
        }
      });
    };
  }, [sections, location.pathname]);

  if (!sections.length) {
    return null;
  }

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 100;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-150 hidden lg:block">
      <nav 
        className="bg-background/95 backdrop-blur-sm rounded-lg shadow-card border border-border p-2"
        aria-label="Page sections"
      >
        <ul className="space-y-2">
          {sections.map((section) => (
            <li key={section.id}>
              <button
                onClick={() => scrollToSection(section.id)}
                className={`w-3 h-3 rounded-full border-2 transition-smooth relative group ${
                  activeSection === section.id
                    ? 'bg-accent border-accent' :'bg-transparent border-text-secondary/30 hover:border-accent/60'
                }`}
                aria-label={`Go to ${section.label} section`}
                title={section.label}
              >
                <span className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-smooth pointer-events-none">
                  {section.label}
                </span>
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default NavigationIndicator;