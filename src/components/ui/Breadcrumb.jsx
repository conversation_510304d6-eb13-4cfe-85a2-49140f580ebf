import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../AppIcon';

const Breadcrumb = () => {
  const location = useLocation();
  
  const breadcrumbMap = {
    '/portfolio-homepage': { label: 'Home', parent: null },
    '/about-page': { label: 'About', parent: '/portfolio-homepage' },
    '/projects-portfolio': { label: 'Projects', parent: '/portfolio-homepage' },
    '/project-detail-page': { label: 'Project Details', parent: '/projects-portfolio' },
    '/contact-page': { label: 'Contact', parent: '/portfolio-homepage' },
    '/blog-articles-page': { label: 'Blog', parent: '/portfolio-homepage' },
  };

  const currentPath = location.pathname;
  const currentBreadcrumb = breadcrumbMap[currentPath];

  if (!currentBreadcrumb || currentPath === '/portfolio-homepage') {
    return null;
  }

  const buildBreadcrumbPath = (path) => {
    const breadcrumbs = [];
    let current = breadcrumbMap[path];
    let currentPath = path;

    while (current) {
      breadcrumbs.unshift({ path: currentPath, label: current.label });
      if (current.parent) {
        currentPath = current.parent;
        current = breadcrumbMap[current.parent];
      } else {
        break;
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = buildBreadcrumbPath(currentPath);

  return (
    <nav 
      className="flex items-center space-x-2 text-sm text-text-secondary mb-6"
      aria-label="Breadcrumb navigation"
    >
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.path} className="flex items-center space-x-2">
            {index > 0 && (
              <Icon 
                name="ChevronRight" 
                size={14} 
                className="text-text-secondary/60" 
              />
            )}
            {index === breadcrumbs.length - 1 ? (
              <span 
                className="text-text-primary font-medium"
                aria-current="page"
              >
                {breadcrumb.label}
              </span>
            ) : (
              <Link
                to={breadcrumb.path}
                className="hover:text-text-primary transition-smooth"
              >
                {breadcrumb.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;