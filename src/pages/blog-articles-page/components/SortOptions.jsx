import React from 'react';
import Icon from '../../../components/AppIcon';

const SortOptions = ({ sortBy, onSortChange }) => {
  const sortOptions = [
    { id: 'newest', label: 'Newest First', icon: 'Calendar' },
    { id: 'popular', label: 'Most Popular', icon: 'TrendingUp' },
    { id: 'alphabetical', label: 'A-Z', icon: 'ArrowUpDown' },
  ];

  return (
    <div className="flex items-center space-x-4 mb-6">
      <div className="flex items-center space-x-2">
        <Icon name="ArrowUpDown" size={16} className="text-text-secondary" />
        <span className="text-sm font-medium text-text-secondary">Sort by:</span>
      </div>
      
      <div className="flex space-x-2">
        {sortOptions.map((option) => (
          <button
            key={option.id}
            onClick={() => onSortChange(option.id)}
            className={`px-3 py-1.5 rounded-md text-sm font-medium transition-smooth flex items-center space-x-1 ${
              sortBy === option.id
                ? 'bg-accent/10 text-accent' :'text-text-secondary hover:text-text-primary hover:bg-surface'
            }`}
          >
            <Icon name={option.icon} size={14} />
            <span>{option.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default SortOptions;