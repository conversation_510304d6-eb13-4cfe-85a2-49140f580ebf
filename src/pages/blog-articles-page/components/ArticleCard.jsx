import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';

const ArticleCard = ({ article, searchTerm }) => {
  const highlightSearchTerm = (text, term) => {
    if (!term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-warning/20 text-warning-foreground">
          {part}
        </mark>
      ) : part
    );
  };

  const handleShare = (platform, e) => {
    e.preventDefault();
    e.stopPropagation();
    
    const url = encodeURIComponent(window.location.origin + '/blog-articles-page');
    const title = encodeURIComponent(article.title);
    
    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${url}&text=${title}`,
      linkedin: `https://linkedin.com/sharing/share-offsite/?url=${url}`,
      facebook: `https://facebook.com/sharer/sharer.php?u=${url}`,
    };
    
    window.open(shareUrls[platform], '_blank', 'width=600,height=400');
  };

  return (
    <article className="bg-background rounded-lg card-shadow overflow-hidden group hover:shadow-interactive transition-layout">
      <Link to="/blog-articles-page" className="block">
        <div className="relative h-48 overflow-hidden">
          <Image
            src={article.image}
            alt={article.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-layout"
          />
          <div className="absolute top-4 left-4">
            <span className="bg-accent text-accent-foreground px-3 py-1 rounded-full text-sm font-medium">
              {article.category}
            </span>
          </div>
        </div>
        
        <div className="p-6">
          <div className="flex items-center space-x-4 mb-3">
            <div className="flex items-center space-x-2 text-text-secondary text-sm">
              <Icon name="Calendar" size={14} />
              <span>{article.date}</span>
            </div>
            <div className="flex items-center space-x-2 text-text-secondary text-sm">
              <Icon name="Clock" size={14} />
              <span>{article.readTime}</span>
            </div>
          </div>
          
          <h3 className="text-lg font-semibold text-primary mb-3 leading-tight group-hover:text-accent transition-smooth">
            {highlightSearchTerm(article.title, searchTerm)}
          </h3>
          
          <p className="text-text-secondary text-sm mb-4 leading-relaxed line-clamp-3">
            {highlightSearchTerm(article.excerpt, searchTerm)}
          </p>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-text-secondary text-sm">
                <Icon name="MessageCircle" size={14} />
                <span>{article.comments} comments</span>
              </div>
              <div className="flex items-center space-x-2 text-text-secondary text-sm">
                <Icon name="Heart" size={14} />
                <span>{article.likes}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-1">
              <button
                onClick={(e) => handleShare('twitter', e)}
                className="p-1.5 text-text-secondary hover:text-accent transition-smooth"
                aria-label="Share on Twitter"
              >
                <Icon name="Twitter" size={14} />
              </button>
              <button
                onClick={(e) => handleShare('linkedin', e)}
                className="p-1.5 text-text-secondary hover:text-accent transition-smooth"
                aria-label="Share on LinkedIn"
              >
                <Icon name="Linkedin" size={14} />
              </button>
              <button
                onClick={(e) => handleShare('facebook', e)}
                className="p-1.5 text-text-secondary hover:text-accent transition-smooth"
                aria-label="Share on Facebook"
              >
                <Icon name="Facebook" size={14} />
              </button>
            </div>
          </div>
        </div>
      </Link>
    </article>
  );
};

export default ArticleCard;