import React from 'react';
import Icon from '../../../components/AppIcon';

const CategoryFilter = ({ categories, activeCategory, onCategoryChange }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center space-x-2 mb-4">
        <Icon name="Filter" size={20} className="text-text-secondary" />
        <h3 className="text-lg font-semibold text-primary">Filter by Category</h3>
      </div>
      
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => onCategoryChange('all')}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-smooth ${
            activeCategory === 'all' ?'bg-accent text-accent-foreground' :'bg-surface text-text-secondary hover:bg-accent/10 hover:text-accent'
          }`}
        >
          All Articles
        </button>
        
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-smooth flex items-center space-x-2 ${
              activeCategory === category.id
                ? 'bg-accent text-accent-foreground'
                : 'bg-surface text-text-secondary hover:bg-accent/10 hover:text-accent'
            }`}
          >
            <Icon name={category.icon} size={16} />
            <span>{category.name}</span>
            <span className="bg-background/20 px-2 py-0.5 rounded-full text-xs">
              {category.count}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;