import React from 'react';

const LoadingState = () => {
  return (
    <div className="space-y-8">
      {/* Featured Article Skeleton */}
      <div className="bg-background rounded-lg card-shadow overflow-hidden animate-pulse">
        <div className="grid lg:grid-cols-2 gap-0">
          <div className="h-64 lg:h-80 bg-surface"></div>
          <div className="p-6 lg:p-8">
            <div className="flex items-center space-x-4 mb-4">
              <div className="h-6 w-20 bg-surface rounded-full"></div>
              <div className="h-4 w-16 bg-surface rounded"></div>
              <div className="h-4 w-16 bg-surface rounded"></div>
            </div>
            <div className="h-8 w-full bg-surface rounded mb-4"></div>
            <div className="h-4 w-full bg-surface rounded mb-2"></div>
            <div className="h-4 w-3/4 bg-surface rounded mb-6"></div>
            <div className="flex items-center justify-between">
              <div className="h-10 w-32 bg-surface rounded"></div>
              <div className="flex space-x-2">
                <div className="h-8 w-8 bg-surface rounded"></div>
                <div className="h-8 w-8 bg-surface rounded"></div>
                <div className="h-8 w-8 bg-surface rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Article Grid Skeleton */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="bg-background rounded-lg card-shadow overflow-hidden animate-pulse">
            <div className="h-48 bg-surface"></div>
            <div className="p-6">
              <div className="flex items-center space-x-4 mb-3">
                <div className="h-4 w-16 bg-surface rounded"></div>
                <div className="h-4 w-16 bg-surface rounded"></div>
              </div>
              <div className="h-6 w-full bg-surface rounded mb-3"></div>
              <div className="h-4 w-full bg-surface rounded mb-2"></div>
              <div className="h-4 w-2/3 bg-surface rounded mb-4"></div>
              <div className="flex items-center justify-between">
                <div className="flex space-x-4">
                  <div className="h-4 w-16 bg-surface rounded"></div>
                  <div className="h-4 w-8 bg-surface rounded"></div>
                </div>
                <div className="flex space-x-1">
                  <div className="h-6 w-6 bg-surface rounded"></div>
                  <div className="h-6 w-6 bg-surface rounded"></div>
                  <div className="h-6 w-6 bg-surface rounded"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LoadingState;