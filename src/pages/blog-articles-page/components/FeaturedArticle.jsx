import React from 'react';

import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const FeaturedArticle = ({ article }) => {
  if (!article) return null;

  const handleShare = (platform) => {
    const url = encodeURIComponent(window.location.origin + '/blog-articles-page');
    const title = encodeURIComponent(article.title);
    
    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${url}&text=${title}`,
      linkedin: `https://linkedin.com/sharing/share-offsite/?url=${url}`,
      facebook: `https://facebook.com/sharer/sharer.php?u=${url}`,
    };
    
    window.open(shareUrls[platform], '_blank', 'width=600,height=400');
  };

  return (
    <div className="mb-12">
      <div className="flex items-center space-x-2 mb-6">
        <Icon name="Star" size={20} className="text-warning" />
        <h2 className="text-xl font-semibold text-primary">Featured Article</h2>
      </div>
      
      <div className="bg-background rounded-lg card-shadow overflow-hidden">
        <div className="grid lg:grid-cols-2 gap-0">
          <div className="relative h-64 lg:h-auto overflow-hidden">
            <Image
              src={article.image}
              alt={article.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 left-4">
              <span className="bg-accent text-accent-foreground px-3 py-1 rounded-full text-sm font-medium">
                Featured
              </span>
            </div>
          </div>
          
          <div className="p-6 lg:p-8 flex flex-col justify-between">
            <div>
              <div className="flex items-center space-x-4 mb-4">
                <span className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm font-medium">
                  {article.category}
                </span>
                <div className="flex items-center space-x-2 text-text-secondary text-sm">
                  <Icon name="Calendar" size={14} />
                  <span>{article.date}</span>
                </div>
                <div className="flex items-center space-x-2 text-text-secondary text-sm">
                  <Icon name="Clock" size={14} />
                  <span>{article.readTime}</span>
                </div>
              </div>
              
              <h3 className="text-2xl font-bold text-primary mb-4 leading-tight">
                {article.title}
              </h3>
              
              <p className="text-text-secondary mb-6 leading-relaxed">
                {article.excerpt}
              </p>
            </div>
            
            <div className="flex items-center justify-between">
              <Button 
                variant="primary"
                iconName="ArrowRight"
                iconPosition="right"
              >
                Read Article
              </Button>
              
              <div className="flex items-center space-x-2">
                <span className="text-sm text-text-secondary">Share:</span>
                <button
                  onClick={() => handleShare('twitter')}
                  className="p-2 text-text-secondary hover:text-accent transition-smooth"
                  aria-label="Share on Twitter"
                >
                  <Icon name="Twitter" size={16} />
                </button>
                <button
                  onClick={() => handleShare('linkedin')}
                  className="p-2 text-text-secondary hover:text-accent transition-smooth"
                  aria-label="Share on LinkedIn"
                >
                  <Icon name="Linkedin" size={16} />
                </button>
                <button
                  onClick={() => handleShare('facebook')}
                  className="p-2 text-text-secondary hover:text-accent transition-smooth"
                  aria-label="Share on Facebook"
                >
                  <Icon name="Facebook" size={16} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturedArticle;