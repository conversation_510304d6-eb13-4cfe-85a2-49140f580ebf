import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';

const Sidebar = ({ popularArticles, categories }) => {
  return (
    <div className="space-y-8">
      {/* Author Bio */}
      <div className="bg-background rounded-lg card-shadow p-6">
        <div className="text-center mb-6">
          <div className="w-20 h-20 mx-auto mb-4 overflow-hidden rounded-full">
            <Image
              src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
              alt="Author Profile"
              className="w-full h-full object-cover"
            />
          </div>
          <h3 className="text-lg font-semibold text-primary mb-2"><PERSON></h3>
          <p className="text-sm text-text-secondary">
            Full-stack developer passionate about creating innovative solutions and sharing knowledge with the community.
          </p>
        </div>
        
        <div className="flex justify-center space-x-4">
          <a
            href="https://twitter.com"
            target="_blank"
            rel="noopener noreferrer"
            className="p-2 text-text-secondary hover:text-accent transition-smooth"
            aria-label="Follow on Twitter"
          >
            <Icon name="Twitter" size={18} />
          </a>
          <a
            href="https://linkedin.com"
            target="_blank"
            rel="noopener noreferrer"
            className="p-2 text-text-secondary hover:text-accent transition-smooth"
            aria-label="Connect on LinkedIn"
          >
            <Icon name="Linkedin" size={18} />
          </a>
          <a
            href="https://github.com"
            target="_blank"
            rel="noopener noreferrer"
            className="p-2 text-text-secondary hover:text-accent transition-smooth"
            aria-label="View GitHub Profile"
          >
            <Icon name="Github" size={18} />
          </a>
        </div>
      </div>

      {/* Popular Articles */}
      <div className="bg-background rounded-lg card-shadow p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Icon name="TrendingUp" size={20} className="text-accent" />
          <h3 className="text-lg font-semibold text-primary">Popular Articles</h3>
        </div>
        
        <div className="space-y-4">
          {popularArticles.map((article, index) => (
            <Link
              key={article.id}
              to="/blog-articles-page"
              className="flex space-x-3 group hover:bg-surface/50 p-2 rounded-md transition-smooth"
            >
              <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded-md">
                <Image
                  src={article.image}
                  alt={article.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-primary group-hover:text-accent transition-smooth line-clamp-2">
                  {article.title}
                </h4>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-xs text-text-secondary">{article.date}</span>
                  <span className="text-xs text-text-secondary">•</span>
                  <div className="flex items-center space-x-1">
                    <Icon name="Eye" size={12} className="text-text-secondary" />
                    <span className="text-xs text-text-secondary">{article.views}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Category Cloud */}
      <div className="bg-background rounded-lg card-shadow p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Icon name="Tag" size={20} className="text-accent" />
          <h3 className="text-lg font-semibold text-primary">Categories</h3>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Link
              key={category.id}
              to="/blog-articles-page"
              className="inline-flex items-center space-x-1 px-3 py-1.5 bg-surface hover:bg-accent/10 text-text-secondary hover:text-accent rounded-full text-sm transition-smooth"
            >
              <Icon name={category.icon} size={14} />
              <span>{category.name}</span>
              <span className="bg-background/60 px-1.5 py-0.5 rounded-full text-xs">
                {category.count}
              </span>
            </Link>
          ))}
        </div>
      </div>

      {/* Newsletter Signup */}
      <div className="bg-gradient-to-br from-accent/5 to-primary/5 rounded-lg p-6">
        <div className="text-center">
          <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon name="Mail" size={24} className="text-accent" />
          </div>
          <h3 className="text-lg font-semibold text-primary mb-2">
            Weekly Newsletter
          </h3>
          <p className="text-sm text-text-secondary mb-4">
            Get the latest articles and insights delivered to your inbox every week.
          </p>
          <Link
            to="/blog-articles-page"
            className="inline-flex items-center space-x-2 px-4 py-2 bg-accent text-accent-foreground rounded-md hover:bg-accent/90 transition-smooth text-sm font-medium"
          >
            <Icon name="ArrowRight" size={16} />
            <span>Subscribe Now</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;