import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const BlogHeader = ({ onSubscribe }) => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubscribe = (e) => {
    e.preventDefault();
    if (email.trim()) {
      onSubscribe(email);
      setIsSubscribed(true);
      setEmail('');
      setTimeout(() => setIsSubscribed(false), 3000);
    }
  };

  return (
    <div className="bg-gradient-to-br from-primary/5 to-accent/5 py-16 lg:py-24">
      <div className="max-w-4xl mx-auto px-6 lg:px-12 text-center">
        <div className="mb-8">
          <h1 className="text-3xl lg:text-5xl font-bold text-primary mb-4">
            Insights & Articles
          </h1>
          <p className="text-lg lg:text-xl text-text-secondary max-w-2xl mx-auto leading-relaxed">
            Sharing knowledge, exploring ideas, and documenting the journey of continuous learning in technology and design.
          </p>
        </div>

        <div className="bg-background/80 backdrop-blur-sm rounded-lg p-6 lg:p-8 card-shadow max-w-md mx-auto">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center">
              <Icon name="Mail" size={24} className="text-accent" />
            </div>
          </div>
          
          <h3 className="text-lg font-semibold text-primary mb-2">
            Stay Updated
          </h3>
          <p className="text-sm text-text-secondary mb-6">
            Get notified when new articles are published
          </p>

          {isSubscribed ? (
            <div className="flex items-center justify-center space-x-2 text-success">
              <Icon name="CheckCircle" size={20} />
              <span className="text-sm font-medium">Successfully subscribed!</span>
            </div>
          ) : (
            <form onSubmit={handleSubscribe} className="space-y-4">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full"
              />
              <Button 
                variant="primary" 
                type="submit"
                fullWidth
                iconName="ArrowRight"
                iconPosition="right"
              >
                Subscribe
              </Button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogHeader;