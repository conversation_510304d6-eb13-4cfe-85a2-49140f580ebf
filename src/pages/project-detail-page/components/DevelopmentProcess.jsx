import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';

const DevelopmentProcess = ({ processSteps }) => {
  const [expandedStep, setExpandedStep] = useState(0);

  const toggleStep = (index) => {
    setExpandedStep(expandedStep === index ? -1 : index);
  };

  return (
    <section className="py-12 lg:py-16 bg-surface">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            Development Process
          </h2>
          <p className="text-text-secondary max-w-2xl mx-auto">
            A detailed look at how this project came to life, from concept to completion
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {processSteps.map((step, index) => (
            <div key={index} className="relative">
              {/* Timeline Line */}
              {index < processSteps.length - 1 && (
                <div className="absolute left-6 top-16 w-0.5 h-16 bg-border"></div>
              )}

              <div className="flex items-start space-x-6 mb-8">
                {/* Step Number */}
                <div className="flex-shrink-0 w-12 h-12 bg-accent text-accent-foreground rounded-full flex items-center justify-center font-semibold">
                  {index + 1}
                </div>

                {/* Step Content */}
                <div className="flex-1">
                  <button
                    onClick={() => toggleStep(index)}
                    className="w-full text-left bg-background rounded-lg p-6 card-shadow hover:shadow-interactive transition-smooth"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-xl font-semibold text-primary mb-2">
                          {step.title}
                        </h3>
                        <p className="text-text-secondary">
                          {step.summary}
                        </p>
                        <div className="flex items-center space-x-4 mt-3 text-sm text-text-secondary">
                          <span className="flex items-center space-x-1">
                            <Icon name="Clock" size={14} />
                            <span>{step.duration}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <Icon name="Calendar" size={14} />
                            <span>{step.timeline}</span>
                          </span>
                        </div>
                      </div>
                      <Icon 
                        name={expandedStep === index ? "ChevronUp" : "ChevronDown"} 
                        size={20} 
                        className="text-text-secondary flex-shrink-0 ml-4"
                      />
                    </div>
                  </button>

                  {/* Expanded Content */}
                  {expandedStep === index && (
                    <div className="mt-4 bg-background rounded-lg p-6 card-shadow">
                      <div className="space-y-6">
                        {/* Detailed Description */}
                        <div>
                          <h4 className="font-semibold text-primary mb-3">Details</h4>
                          <p className="text-text-secondary leading-relaxed">
                            {step.details}
                          </p>
                        </div>

                        {/* Key Decisions */}
                        {step.decisions && (
                          <div>
                            <h4 className="font-semibold text-primary mb-3">Key Decisions</h4>
                            <ul className="space-y-2">
                              {step.decisions.map((decision, decisionIndex) => (
                                <li key={decisionIndex} className="flex items-start space-x-2">
                                  <Icon name="ArrowRight" size={16} className="text-accent mt-0.5 flex-shrink-0" />
                                  <span className="text-text-secondary">{decision}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Wireframes/Images */}
                        {step.images && step.images.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-primary mb-3">Visual Documentation</h4>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                              {step.images.map((image, imageIndex) => (
                                <div key={imageIndex} className="rounded-lg overflow-hidden bg-surface">
                                  <Image
                                    src={image.url}
                                    alt={image.caption}
                                    className="w-full h-32 object-cover"
                                  />
                                  <div className="p-3">
                                    <p className="text-sm text-text-secondary">{image.caption}</p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Tools Used */}
                        {step.tools && (
                          <div>
                            <h4 className="font-semibold text-primary mb-3">Tools & Technologies</h4>
                            <div className="flex flex-wrap gap-2">
                              {step.tools.map((tool, toolIndex) => (
                                <span
                                  key={toolIndex}
                                  className="px-3 py-1 bg-accent/10 text-accent text-sm rounded-full"
                                >
                                  {tool}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DevelopmentProcess;