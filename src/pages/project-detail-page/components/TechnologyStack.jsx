import React, { useState } from 'react';
import Image from '../../../components/AppImage';

const TechnologyStack = ({ technologies }) => {
  const [hoveredTech, setHoveredTech] = useState(null);

  const techCategories = [
    { name: "Frontend", techs: technologies.filter(tech => tech.category === "frontend") },
    { name: "Backend", techs: technologies.filter(tech => tech.category === "backend") },
    { name: "Database", techs: technologies.filter(tech => tech.category === "database") },
    { name: "Tools", techs: technologies.filter(tech => tech.category === "tools") }
  ];

  return (
    <section className="py-12 lg:py-16 bg-surface">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            Technology Stack
          </h2>
          <p className="text-text-secondary max-w-2xl mx-auto">
            The tools and technologies that powered this project
          </p>
        </div>

        <div className="space-y-8">
          {techCategories.map((category, categoryIndex) => (
            category.techs.length > 0 && (
              <div key={categoryIndex} className="bg-background rounded-lg p-6 lg:p-8 card-shadow">
                <h3 className="text-lg font-semibold text-primary mb-6">
                  {category.name}
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                  {category.techs.map((tech, techIndex) => (
                    <div
                      key={techIndex}
                      className="relative group"
                      onMouseEnter={() => setHoveredTech(`${categoryIndex}-${techIndex}`)}
                      onMouseLeave={() => setHoveredTech(null)}
                    >
                      <div className="bg-surface rounded-lg p-4 text-center transition-smooth hover:shadow-card hover:-translate-y-1 cursor-pointer">
                        <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                          <Image
                            src={tech.logo}
                            alt={`${tech.name} logo`}
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="text-sm font-medium text-primary">
                          {tech.name}
                        </div>
                      </div>
                      
                      {/* Tooltip */}
                      {hoveredTech === `${categoryIndex}-${techIndex}` && (
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-primary text-primary-foreground text-xs rounded whitespace-nowrap z-10 opacity-0 group-hover:opacity-100 transition-smooth">
                          {tech.description}
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-primary"></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )
          ))}
        </div>
      </div>
    </section>
  );
};

export default TechnologyStack;