import React from 'react';
import { Link } from 'react-router-dom';
import Image from '../../../components/AppImage';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RelatedProjects = ({ projects }) => {
  if (!projects || projects.length === 0) return null;

  return (
    <section className="py-12 lg:py-16 bg-background">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            Related Projects
          </h2>
          <p className="text-text-secondary max-w-2xl mx-auto">
            Explore more of my work in similar domains and technologies
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div
              key={index}
              className="bg-surface rounded-lg overflow-hidden card-shadow hover:shadow-interactive transition-smooth group"
            >
              {/* Project Image */}
              <div className="aspect-video overflow-hidden bg-background">
                <Image
                  src={project.image}
                  alt={`${project.title} preview`}
                  className="w-full h-full object-cover group-hover:scale-105 transition-smooth"
                />
              </div>

              {/* Project Info */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="px-2 py-1 bg-accent/10 text-accent text-xs font-medium rounded">
                    {project.category}
                  </span>
                  <span className="text-xs text-text-secondary">
                    {project.year}
                  </span>
                </div>

                <h3 className="text-lg font-semibold text-primary mb-2 group-hover:text-accent transition-smooth">
                  {project.title}
                </h3>

                <p className="text-text-secondary text-sm mb-4 line-clamp-2">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {project.technologies.slice(0, 3).map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 py-1 bg-background text-text-secondary text-xs rounded"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-2 py-1 bg-background text-text-secondary text-xs rounded">
                      +{project.technologies.length - 3}
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between">
                  <Link
                    to="/project-detail-page"
                    className="text-accent hover:text-accent/80 text-sm font-medium transition-smooth flex items-center space-x-1"
                  >
                    <span>View Details</span>
                    <Icon name="ArrowRight" size={14} />
                  </Link>

                  <div className="flex items-center space-x-2">
                    {project.liveUrl && (
                      <button
                        onClick={() => window.open(project.liveUrl, '_blank')}
                        className="w-8 h-8 bg-background hover:bg-accent/10 rounded-full flex items-center justify-center transition-smooth"
                        title="View Live Demo"
                      >
                        <Icon name="ExternalLink" size={14} className="text-text-secondary hover:text-accent" />
                      </button>
                    )}
                    {project.githubUrl && (
                      <button
                        onClick={() => window.open(project.githubUrl, '_blank')}
                        className="w-8 h-8 bg-background hover:bg-accent/10 rounded-full flex items-center justify-center transition-smooth"
                        title="View Source Code"
                      >
                        <Icon name="Github" size={14} className="text-text-secondary hover:text-accent" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Projects Button */}
        <div className="text-center mt-12">
          <Link to="/projects-portfolio">
            <Button
              variant="outline"
              size="lg"
              iconName="FolderOpen"
              iconPosition="left"
            >
              View All Projects
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default RelatedProjects;