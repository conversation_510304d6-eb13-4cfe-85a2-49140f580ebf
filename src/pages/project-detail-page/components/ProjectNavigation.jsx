import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ProjectNavigation = ({ previousProject, nextProject }) => {
  return (
    <section className="py-8 bg-surface border-t border-border">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
          {/* Previous Project */}
          <div className="flex-1">
            {previousProject ? (
              <Link
                to="/project-detail-page"
                className="group flex items-center space-x-3 p-4 rounded-lg hover:bg-background transition-smooth"
              >
                <div className="w-10 h-10 bg-background group-hover:bg-accent/10 rounded-full flex items-center justify-center transition-smooth">
                  <Icon name="ChevronLeft" size={20} className="text-text-secondary group-hover:text-accent" />
                </div>
                <div className="text-left">
                  <div className="text-xs text-text-secondary uppercase tracking-wide">Previous</div>
                  <div className="font-medium text-primary group-hover:text-accent transition-smooth">
                    {previousProject.title}
                  </div>
                </div>
              </Link>
            ) : (
              <div className="opacity-50">
                <div className="flex items-center space-x-3 p-4">
                  <div className="w-10 h-10 bg-background rounded-full flex items-center justify-center">
                    <Icon name="ChevronLeft" size={20} className="text-text-secondary" />
                  </div>
                  <div className="text-left">
                    <div className="text-xs text-text-secondary uppercase tracking-wide">Previous</div>
                    <div className="font-medium text-text-secondary">No previous project</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Back to Projects */}
          <div className="flex-shrink-0">
            <Link to="/projects-portfolio">
              <Button
                variant="ghost"
                size="sm"
                iconName="Grid3X3"
                iconPosition="left"
              >
                All Projects
              </Button>
            </Link>
          </div>

          {/* Next Project */}
          <div className="flex-1 flex justify-end">
            {nextProject ? (
              <Link
                to="/project-detail-page"
                className="group flex items-center space-x-3 p-4 rounded-lg hover:bg-background transition-smooth"
              >
                <div className="text-right">
                  <div className="text-xs text-text-secondary uppercase tracking-wide">Next</div>
                  <div className="font-medium text-primary group-hover:text-accent transition-smooth">
                    {nextProject.title}
                  </div>
                </div>
                <div className="w-10 h-10 bg-background group-hover:bg-accent/10 rounded-full flex items-center justify-center transition-smooth">
                  <Icon name="ChevronRight" size={20} className="text-text-secondary group-hover:text-accent" />
                </div>
              </Link>
            ) : (
              <div className="opacity-50">
                <div className="flex items-center space-x-3 p-4">
                  <div className="text-right">
                    <div className="text-xs text-text-secondary uppercase tracking-wide">Next</div>
                    <div className="font-medium text-text-secondary">No next project</div>
                  </div>
                  <div className="w-10 h-10 bg-background rounded-full flex items-center justify-center">
                    <Icon name="ChevronRight" size={20} className="text-text-secondary" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectNavigation;