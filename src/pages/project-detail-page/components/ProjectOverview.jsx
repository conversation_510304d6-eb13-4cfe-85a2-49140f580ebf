import React from 'react';
import Icon from '../../../components/AppIcon';

const ProjectOverview = ({ project }) => {
  const overviewSections = [
    {
      title: "Challenge",
      content: project.challenge,
      icon: "Target",
      color: "text-error"
    },
    {
      title: "Solution",
      content: project.solution,
      icon: "Lightbulb",
      color: "text-warning"
    },
    {
      title: "Results",
      content: project.results,
      icon: "TrendingUp",
      color: "text-success"
    }
  ];

  return (
    <section className="py-12 lg:py-16 bg-background">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            Project Overview
          </h2>
          <p className="text-text-secondary max-w-2xl mx-auto">
            Understanding the problem, crafting the solution, and measuring the impact
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {overviewSections.map((section, index) => (
            <div key={index} className="bg-surface rounded-lg p-6 lg:p-8 card-shadow">
              <div className="flex items-center space-x-3 mb-4">
                <div className={`w-10 h-10 rounded-lg bg-surface flex items-center justify-center ${section.color}`}>
                  <Icon name={section.icon} size={20} />
                </div>
                <h3 className="text-xl font-semibold text-primary">
                  {section.title}
                </h3>
              </div>
              <p className="text-text-secondary leading-relaxed">
                {section.content}
              </p>
            </div>
          ))}
        </div>

        {/* Key Metrics */}
        <div className="mt-12 bg-gradient-to-r from-accent/5 to-primary/5 rounded-lg p-6 lg:p-8">
          <h3 className="text-xl font-semibold text-primary mb-6 text-center">
            Key Metrics & Impact
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {project.metrics.map((metric, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl lg:text-3xl font-bold text-accent mb-1">
                  {metric.value}
                </div>
                <div className="text-sm text-text-secondary">
                  {metric.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectOverview;