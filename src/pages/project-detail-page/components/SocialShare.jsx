import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SocialShare = ({ project }) => {
  const [copied, setCopied] = useState(false);
  
  const currentUrl = window.location.href;
  const shareText = `Check out this amazing project: ${project.title}`;

  const shareLinks = [
    {
      name: 'Twitter',
      icon: 'Twitter',
      url: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(currentUrl)}`,
      color: 'hover:text-blue-500'
    },
    {
      name: 'LinkedIn',
      icon: 'Linkedin',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(currentUrl)}`,
      color: 'hover:text-blue-600'
    },
    {
      name: 'Facebook',
      icon: 'Facebook',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`,
      color: 'hover:text-blue-700'
    }
  ];

  const handleShare = (url) => {
    window.open(url, '_blank', 'width=600,height=400');
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(currentUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  return (
    <section className="py-8 bg-background border-t border-border">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Share This Project
          </h3>
          <p className="text-text-secondary mb-6">
            Help others discover this work
          </p>

          <div className="flex items-center justify-center space-x-4">
            {/* Social Share Buttons */}
            {shareLinks.map((social, index) => (
              <button
                key={index}
                onClick={() => handleShare(social.url)}
                className={`w-12 h-12 bg-surface hover:bg-accent/10 rounded-full flex items-center justify-center transition-smooth ${social.color}`}
                title={`Share on ${social.name}`}
              >
                <Icon name={social.icon} size={20} />
              </button>
            ))}

            {/* Copy Link Button */}
            <button
              onClick={handleCopyLink}
              className="w-12 h-12 bg-surface hover:bg-accent/10 rounded-full flex items-center justify-center transition-smooth hover:text-accent"
              title="Copy link"
            >
              <Icon name={copied ? "Check" : "Link"} size={20} />
            </button>
          </div>

          {copied && (
            <div className="mt-4 text-sm text-success">
              Link copied to clipboard!
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default SocialShare;