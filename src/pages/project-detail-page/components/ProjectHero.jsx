import React from 'react';
import Button from '../../../components/ui/Button';
import Image from '../../../components/AppImage';
import Icon from '../../../components/AppIcon';

const ProjectHero = ({ project }) => {
  const handleLiveDemo = () => {
    window.open(project.liveUrl, '_blank');
  };

  const handleSourceCode = () => {
    window.open(project.githubUrl, '_blank');
  };

  return (
    <section className="relative bg-gradient-to-br from-primary/5 to-accent/5 py-12 lg:py-20">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Project Image */}
          <div className="order-2 lg:order-1">
            <div className="relative rounded-lg overflow-hidden shadow-interactive bg-white">
              <Image
                src={project.heroImage}
                alt={`${project.title} project screenshot`}
                className="w-full h-64 sm:h-80 lg:h-96 object-cover"
              />
              {project.isVideo && (
                <div className="absolute inset-0 flex items-center justify-center bg-primary/20">
                  <button className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-smooth">
                    <Icon name="Play" size={24} className="text-primary ml-1" />
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Project Info */}
          <div className="order-1 lg:order-2 space-y-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <span className="px-3 py-1 bg-accent/10 text-accent text-sm font-medium rounded-full">
                  {project.category}
                </span>
                <span className="text-text-secondary text-sm">
                  {project.year}
                </span>
              </div>
              
              <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-primary leading-tight">
                {project.title}
              </h1>
              
              <p className="text-lg text-text-secondary leading-relaxed">
                {project.description}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                variant="primary"
                size="lg"
                iconName="ExternalLink"
                iconPosition="right"
                onClick={handleLiveDemo}
                className="flex-1 sm:flex-none"
              >
                View Live Demo
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                iconName="Github"
                iconPosition="left"
                onClick={handleSourceCode}
                className="flex-1 sm:flex-none"
              >
                Source Code
              </Button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t border-border">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{project.duration}</div>
                <div className="text-sm text-text-secondary">Duration</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{project.teamSize}</div>
                <div className="text-sm text-text-secondary">Team Size</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{project.platform}</div>
                <div className="text-sm text-text-secondary">Platform</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectHero;