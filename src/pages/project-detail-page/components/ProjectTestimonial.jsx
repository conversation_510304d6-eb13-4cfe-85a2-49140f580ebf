import React from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';

const ProjectTestimonial = ({ testimonial, metrics }) => {
  if (!testimonial) return null;

  return (
    <section className="py-12 lg:py-16 bg-gradient-to-br from-accent/5 to-primary/5">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="max-w-4xl mx-auto">
          {/* Testimonial */}
          <div className="text-center mb-12">
            <div className="mb-8">
              <Icon name="Quote" size={48} className="text-accent/30 mx-auto mb-6" />
              <blockquote className="text-xl lg:text-2xl text-primary font-medium leading-relaxed mb-8">
                "{testimonial.quote}"
              </blockquote>
            </div>

            <div className="flex items-center justify-center space-x-4">
              <div className="w-16 h-16 rounded-full overflow-hidden bg-surface">
                <Image
                  src={testimonial.avatar}
                  alt={`${testimonial.name} avatar`}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="text-left">
                <div className="font-semibold text-primary">{testimonial.name}</div>
                <div className="text-text-secondary">{testimonial.role}</div>
                <div className="text-sm text-text-secondary">{testimonial.company}</div>
              </div>
            </div>
          </div>

          {/* Project Impact Metrics */}
          {metrics && metrics.length > 0 && (
            <div className="bg-background rounded-lg p-6 lg:p-8 card-shadow">
              <h3 className="text-xl font-semibold text-primary mb-6 text-center">
                Project Impact
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {metrics.map((metric, index) => (
                  <div key={index} className="text-center">
                    <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                      <Icon name={metric.icon} size={24} className="text-accent" />
                    </div>
                    <div className="text-2xl lg:text-3xl font-bold text-primary mb-1">
                      {metric.value}
                    </div>
                    <div className="text-sm text-text-secondary font-medium">
                      {metric.label}
                    </div>
                    {metric.description && (
                      <div className="text-xs text-text-secondary mt-1">
                        {metric.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ProjectTestimonial;