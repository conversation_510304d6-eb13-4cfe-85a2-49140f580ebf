import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import NavigationIndicator from '../../components/ui/NavigationIndicator';
import HeroSection from './components/HeroSection';
import BiographySection from './components/BiographySection';
import TimelineSection from './components/TimelineSection';
import SkillsSection from './components/SkillsSection';
import CallToActionSection from './components/CallToActionSection';

const AboutPage = () => {
  const navigationSections = [
    { id: 'hero', label: 'About Me' },
    { id: 'biography', label: 'My Story' },
    { id: 'timeline', label: 'Career Journey' },
    { id: 'skills', label: 'Skills & Expertise' },
    { id: 'cta', label: 'Let\'s Connect' }
  ];

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <Helmet>
        <title>About - <PERSON> | Full Stack Developer</title>
        <meta 
          name="description" 
          content="Learn about <PERSON>, a passionate full-stack developer with 5+ years of experience creating innovative digital solutions. Discover my background, skills, and career journey." 
        />
        <meta name="keywords" content="about, full stack developer, web developer, react developer, career, skills, experience" />
        <meta property="og:title" content="About - John Doe | Full Stack Developer" />
        <meta property="og:description" content="Learn about John Doe, a passionate full-stack developer with 5+ years of experience creating innovative digital solutions." />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="/about-page" />
      </Helmet>

      <div className="min-h-screen bg-background">
        <Header />
        
        <main className="pt-16 lg:pt-20">
          <div className="max-w-7xl mx-auto px-6 lg:px-12 py-6">
            <Breadcrumb />
          </div>

          <HeroSection />
          <BiographySection />
          <TimelineSection />
          <SkillsSection />
          <CallToActionSection />
        </main>

        <NavigationIndicator sections={navigationSections} />
      </div>
    </>
  );
};

export default AboutPage;