import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import { getWorkExperience } from '../../../utils/resumeData';
import { format } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';

const TimelineSection = () => {
  const [expandedEntry, setExpandedEntry] = useState(null);
  const timelineData = getWorkExperience();

  const toggleEntry = (entryId) => {
    console.log('Toggling entry:', entryId, 'Current expanded:', expandedEntry);
    setExpandedEntry(prev => {
      const newState = prev === entryId ? null : entryId;
      console.log('New state:', newState);
      return newState;
    });
  };

  const renderHighlight = (highlight, index) => {
    const isResponsibility = highlight.startsWith('• ');
    const content = isResponsibility ? highlight.substring(2) : highlight;
    
    return (
      <motion.li 
        key={index}
        className="flex items-start space-x-3 mb-3"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
      >
        <span className={`mt-1 flex-shrink-0 ${isResponsibility ? 'text-accent' : 'text-primary'}`}>
          <Icon name={isResponsibility ? 'CheckCircle' : 'Award'} size={16} />
        </span>
        <span className="text-text-secondary">{content}</span>
      </motion.li>
    );
  };

  const formatDateRange = (startDate, endDate) => {
    const formatDate = (dateString) => {
      if (!dateString) return 'Present';
      const date = new Date(dateString);
      return format(date, 'MMM yyyy');
    };
    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };

  const getCompanyLogo = (companyName) => {
    const logoMap = {
      'AHEAD': 'https://media.licdn.com/dms/image/C4E0BAQHQr8ZhqvRKdA/company-logo_200_200/0/1630639068639/ahead_2_logo?e=2147483647&v=beta&t=XYvVJQKJQZQKJQKJQZQKJQKJQZQKJQKJQZQKJQKJQZQ',
      'Amazon Web Services (AWS)': 'https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg',
      'Red Hat': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Red_Hat_Logo_Lockup_RGB_Red.svg/1200px-Red_Hat_Logo_Lockup_RGB_Red.svg.png',
      'FICO': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ab/FICO_Logo.svg/1200px-FICO_Logo.svg.png',
      'American Express': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/fa/American_Express_logo_%282018%29.svg/1200px-American_Express_logo_%282018%29.svg.png',
      'VCE': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/VCE_company_logo.svg/1200px-VCE_company_logo.svg.png',
      'Microsoft': 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/96/Microsoft_logo_%282012%29.svg/1200px-Microsoft_logo_%282012%29.svg.png'
    };
    return logoMap[companyName] || '/images/company-placeholder.png';
  };

  const getCompanyUrl = (companyName) => {
    const urlMap = {
      'AHEAD': 'https://www.thinkahead.com',
      'Amazon Web Services (AWS)': 'https://aws.amazon.com',
      'Red Hat': 'https://www.redhat.com',
      'FICO': 'https://www.fico.com',
      'American Express': 'https://www.americanexpress.com',
      'VCE': 'https://www.delltechnologies.com',
      'Microsoft': 'https://www.microsoft.com'
    };
    return urlMap[companyName] || '#';
  };

  return (
    <section id="experience" className="py-16 lg:py-20 bg-background overflow-hidden">
      <div className="max-w-6xl mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-2xl lg:text-4xl font-bold text-primary mb-4">
            Professional Experience
          </h2>
          <p className="text-lg text-text-secondary max-w-3xl mx-auto">
            A journey through my career, highlighting key roles, achievements, and the impact I've made at leading technology companies.
          </p>
        </div>

        <div className="relative max-w-5xl mx-auto">
          {/* Vertical line */}
          <div className="absolute left-0 md:left-1/2 w-0.5 h-full bg-gradient-to-b from-accent/20 via-accent/40 to-accent/20 hidden md:block"></div>
          
          <div className="space-y-16 md:space-y-24">
            {timelineData.map((entry, index) => {
              const isEven = index % 2 === 0;
              const companyUrl = getCompanyUrl(entry.company);
              const logoUrl = getCompanyLogo(entry.company);
              
              return (
                <div 
                  key={entry.id} 
                  className={`relative group ${isEven ? 'md:pr-8 md:text-right' : 'md:pl-8'}`}
                  style={{ 
                    marginLeft: isEven ? '0' : 'auto',
                    width: '100%',
                    maxWidth: 'calc(50% - 40px)'
                  }}
                >
                  {/* Timeline dot */}
                  <div 
                    className={`absolute top-6 w-4 h-4 bg-accent rounded-full border-4 border-background z-10 hidden md:block ${
                      isEven ? 'right-[-8px]' : 'left-[-8px]'
                    }`}
                  />

                  <div className={`relative ${isEven ? 'md:mr-12' : 'md:ml-12'}`}>
                    <div className="bg-surface rounded-2xl border border-border/50 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:border-accent/30">
                      <div className="p-6 md:p-8">
                        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 mb-4">
                          <div className="flex items-center space-x-4">
                            <div className="w-16 h-16 rounded-xl overflow-hidden border border-border/50 bg-white p-1.5 flex-shrink-0">
                              <Image
                                src={logoUrl}
                                alt={`${entry.company} logo`}
                                className="w-full h-full object-contain"
                                width={64}
                                height={64}
                              />
                            </div>
                            <div>
                              <h3 className="text-xl font-bold text-primary">
                                {entry.position}
                              </h3>
                              <div className="flex items-center space-x-2 mt-1">
                                <a 
                                  href={companyUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-accent hover:underline font-medium flex items-center"
                                >
                                  {entry.company}
                                  <Icon name="ExternalLink" size={14} className="ml-1" />
                                </a>
                              </div>
                            </div>
                          </div>
                          
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-accent/10 text-accent">
                            {formatDateRange(entry.startDate, entry.endDate)}
                          </span>
                        </div>

                        <div className="mt-4">
                          <p className="text-text-secondary leading-relaxed">
                            {entry.summary}
                          </p>
                        </div>

                        {entry.highlights && entry.highlights.length > 0 && (
                          <div className="mt-6">
                            <h4 className="text-sm font-semibold text-primary mb-3 uppercase tracking-wider">
                              Key Achievements
                            </h4>
                            <ul className="space-y-3">
                              {entry.highlights.slice(0, 3).map((highlight, i) => (
                                <li key={i} className="flex items-start space-x-3">
                                  <span className="text-accent mt-1 flex-shrink-0">
                                    <Icon name="CheckCircle" size={16} />
                                  </span>
                                  <span className="text-text-secondary">{highlight}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {entry.technologies && entry.technologies.length > 0 && (
                          <div className="mt-6">
                            <h4 className="text-sm font-semibold text-primary mb-3 uppercase tracking-wider">
                              Technologies & Skills
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {entry.technologies.slice(0, 8).map((tech, i) => (
                                <span
                                  key={i}
                                  className="px-3 py-1 bg-background text-text-secondary text-xs font-medium rounded-full border border-border/30"
                                >
                                  {tech}
                                </span>
                              ))}
                              {entry.technologies.length > 8 && (
                                <span className="px-3 py-1 bg-background text-text-tertiary text-xs font-medium rounded-full border border-border/30">
                                  +{entry.technologies.length - 8} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}

                        <AnimatePresence>
                          {String(expandedEntry) === String(entry.id) && (
                            <motion.div 
                              className="mt-6 pt-6 border-t border-border/20"
                              initial={{ opacity: 0, height: 0, overflow: 'hidden' }}
                              animate={{ 
                                opacity: 1, 
                                height: 'auto',
                                transition: { duration: 0.3 }
                              }}
                              exit={{ 
                                opacity: 0, 
                                height: 0,
                                transition: { duration: 0.2 }
                              }}
                            >
                              <div className="space-y-6">
                                {entry.highlights && entry.highlights.length > 0 && (
                                  <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: 0.1 }}
                                  >
                                    <h4 className="text-sm font-semibold text-primary mb-3 uppercase tracking-wider flex items-center">
                                      <Icon name="Award" size={16} className="mr-2" />
                                      Key Responsibilities & Achievements
                                    </h4>
                                    <ul className="space-y-3">
                                      {entry.highlights.map((highlight, i) => renderHighlight(highlight, i))}
                                    </ul>
                                  </motion.div>
                                )}

                                {entry.technologies && entry.technologies.length > 0 && (
                                  <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: 0.2 }}
                                  >
                                    <h4 className="text-sm font-semibold text-primary mb-3 uppercase tracking-wider flex items-center">
                                      <Icon name="Code" size={16} className="mr-2" />
                                      Technologies & Tools
                                    </h4>
                                    <div className="flex flex-wrap gap-2">
                                      {entry.technologies.map((tech, i) => (
                                        <motion.span
                                          key={i}
                                          className="px-3 py-1.5 bg-background text-text-secondary text-xs font-medium rounded-full border border-border/30 hover:bg-accent/5 hover:border-accent/30 transition-colors"
                                          initial={{ opacity: 0, scale: 0.9 }}
                                          animate={{ opacity: 1, scale: 1 }}
                                          transition={{ 
                                            duration: 0.2, 
                                            delay: 0.1 + (i * 0.02)
                                          }}
                                        >
                                          {tech}
                                        </motion.span>
                                      ))}
                                    </div>
                                  </motion.div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        <div className="mt-6">
                          <button
                            onClick={() => toggleEntry(entry.id)}
                            className="inline-flex items-center text-sm font-medium text-accent hover:text-accent/80 transition-colors group"
                          >
                            <span>{String(expandedEntry) === String(entry.id) ? 'Show Less' : 'Show More'}</span>
                            <motion.span
                              animate={{ rotate: expandedEntry === entry.id ? 180 : 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Icon 
                                name="ChevronDown" 
                                size={16} 
                                className="ml-1 group-hover:translate-y-0.5 transition-transform" 
                              />
                            </motion.span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TimelineSection;
