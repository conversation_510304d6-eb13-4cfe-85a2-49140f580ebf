import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import { getSkills, getCertifications } from '../../../utils/resumeData';

const SkillsSection = () => {
  const [activeCategory, setActiveCategory] = useState('technical');

  const skillCategories = getSkills();
  const certifications = getCertifications();
  const skillCategoriesArray = Object.entries(skillCategories).map(([id, category]) => ({
    id,
    ...category
  }));

  const getSkillColor = (level) => {
    const safeLevel = Number(level) || 0;
    if (safeLevel >= 90) return 'bg-success';
    if (safeLevel >= 80) return 'bg-accent';
    if (safeLevel >= 70) return 'bg-warning';
    return 'bg-text-secondary';
  };

  const getSkillTextColor = (level) => {
    const safeLevel = Number(level) || 0;
    if (safeLevel >= 90) return 'text-success';
    if (safeLevel >= 80) return 'text-accent';
    if (safeLevel >= 70) return 'text-warning';
    return 'text-text-secondary';
  };

  return (
    <section id="skills" className="py-16 lg:py-20 bg-background">
      <div className="max-w-6xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            Skills & Expertise
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            A comprehensive overview of my technical abilities, design skills, and professional competencies.
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {Object.entries(skillCategories).map(([id, category]) => (
            <button
              key={id}
              onClick={() => setActiveCategory(id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-smooth ${
                activeCategory === id
                  ? 'bg-accent text-accent-foreground'
                  : 'bg-surface text-text-secondary hover:text-text-primary hover:bg-background'
              }`}
            >
              <Icon name={category.icon} size={18} />
              <span>{category.title}</span>
            </button>
          ))}
        </div>

        {/* Skills Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
          {skillCategories[activeCategory].skills.map((skill, index) => (
            <div
              key={index}
              className="bg-surface rounded-xl p-6 border border-border hover:shadow-card transition-smooth"
            >
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="font-semibold text-primary">{skill.name}</h3>
                  <p className="text-sm text-text-secondary">
                    {skill.years || 0} years experience
                  </p>
                </div>
                <div className={`text-lg font-bold ${getSkillTextColor(skill.level)}`}>
                  {Number(skill.level) || 0}%
                </div>
              </div>
              
              <div className="w-full bg-border rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-1000 ${getSkillColor(skill.level)}`}
                  style={{ width: `${Number(skill.level) || 0}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>

        {/* Certifications */}
        <div className="bg-surface rounded-2xl p-8 border border-border">
          <h3 className="text-xl font-semibold text-primary mb-6 text-center">
            Certifications & Achievements
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {certifications.map((cert, index) => (
              <div
                key={index}
                className="bg-background rounded-xl p-6 border border-border text-center hover:shadow-card transition-smooth"
              >
                <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Icon name="award" size={24} className="text-accent" />
                </div>
                
                <h4 className="font-semibold text-primary mb-2">{cert.name}</h4>
                <p className="text-sm text-text-secondary mb-2">{cert.issuer}</p>
                <p className="text-xs text-text-secondary mb-3">{cert.date}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Skills Summary */}
        <div className="mt-12 text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">{skillCategoriesArray.reduce((total, cat) => total + cat.skills.length, 0)}</div>
              <div className="text-sm text-text-secondary">Total Skills</div>
            </div>
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">{skillCategoriesArray.length}</div>
              <div className="text-sm text-text-secondary">Skill Categories</div>
            </div>
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">{certifications.length}</div>
              <div className="text-sm text-text-secondary">Certifications</div>
            </div>
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">{Math.max(...skillCategoriesArray.flatMap(cat => cat.skills.map(skill => skill.years)))}</div>
              <div className="text-sm text-text-secondary">Max Experience (Years)</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;