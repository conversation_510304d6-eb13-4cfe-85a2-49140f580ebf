import React from 'react';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';
import { getBasicInfo, getCareerStats } from '../../../utils/resumeData';

const HeroSection = () => {
  const basicInfo = getBasicInfo();
  const careerStats = getCareerStats();

  const handleDownloadResume = () => {
    // Mock resume download functionality
    const link = document.createElement('a');
    link.href = '#';
    link.download = `${basicInfo.name.replace(' ', '-').toLowerCase()}-resume.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section id="hero" className="relative bg-gradient-to-br from-primary/5 to-accent/5 py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Left Column - Text Content */}
          <div className="order-2 lg:order-1">
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-primary mb-4">
                  About {basicInfo.name}
                </h1>
                <p className="text-lg lg:text-xl text-text-secondary leading-relaxed">
                  {basicInfo.summary}
                </p>
              </div>
              
              <div className="flex flex-wrap gap-4">
                <Button 
                  variant="primary" 
                  iconName="Download" 
                  iconPosition="left"
                  onClick={handleDownloadResume}
                >
                  Download Resume
                </Button>
                <Button 
                  variant="outline" 
                  iconName="Mail" 
                  iconPosition="left"
                >
                  Get In Touch
                </Button>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-border">
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-accent">{careerStats.yearsExperience}+</div>
                  <div className="text-sm text-text-secondary">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-accent">{careerStats.companiesWorked}</div>
                  <div className="text-sm text-text-secondary">Companies</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-accent">{careerStats.certifications}+</div>
                  <div className="text-sm text-text-secondary">Certifications</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Professional Photo */}
          <div className="order-1 lg:order-2">
            <div className="relative">
              <div className="relative overflow-hidden rounded-2xl shadow-interactive">
                <Image
                  src="/SCR-20250626-jqdc.png"
                  alt={`${basicInfo.name} - Professional Portrait`}
                  className="w-full h-96 lg:h-[500px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent"></div>
              </div>
              
              {/* Floating Achievement Badge */}
              <div className="absolute -bottom-6 -right-6 bg-background rounded-xl shadow-interactive p-4 border border-border">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center">
                    <Icon name="Award" size={24} className="text-success" />
                  </div>
                  <div>
                    <div className="font-semibold text-primary">Certified</div>
                    <div className="text-sm text-text-secondary">{basicInfo.label}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;