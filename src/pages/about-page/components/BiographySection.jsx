import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import { getBasicInfo, getVolunteerWork, getAwards, getPublications } from '../../../utils/resumeData';
import { Link } from 'react-router-dom';

const BiographySection = () => {
  const [expandedSection, setExpandedSection] = useState(null);
  const basicInfo = getBasicInfo();
  const volunteerWork = getVolunteerWork();
  const awards = getAwards();
  const publications = getPublications();

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
  };

  const biographySections = [
    {
      id: 'background',
      title: 'Professional Background',
      icon: 'Briefcase',
      content: (
        <div className="space-y-4">
          <p className="text-text-secondary leading-relaxed">
            {basicInfo.summary}
          </p>
          <div className="mt-4">
            <h4 className="font-semibold text-primary mb-2">Key Areas of Expertise:</h4>
            <ul className="list-disc pl-5 space-y-2 text-text-secondary">
              <li>Cloud Architecture & Platform Engineering</li>
              <li>Digital Transformation & Modernization</li>
              <li>AI/ML Integration</li>
              <li>Open Source Technologies</li>
              <li>Enterprise Solutions Design</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'achievements',
      title: 'Notable Achievements',
      icon: 'Award',
      content: (
        <div className="space-y-8">
          {awards.length > 0 ? (
            <div className="space-y-8">
              {awards.map((award, index) => (
                <div key={index} className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-accent/20 to-purple-500/10 rounded-lg opacity-0 group-hover:opacity-100 blur transition duration-200"></div>
                  <div className="relative bg-background/80 backdrop-blur-sm p-6 rounded-lg border border-border/50 hover:border-accent/30 transition-colors">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="text-lg font-semibold text-primary">{award.title}</h4>
                        <div className="flex items-center mt-1 text-sm text-text-secondary">
                          <span className="flex items-center">
                            <Icon name="Award" size={14} className="mr-1.5 text-amber-400" />
                            {award.awarder}
                          </span>
                          <span className="mx-2">•</span>
                          <span className="flex items-center">
                            <Icon name="Calendar" size={14} className="mr-1.5 text-text-tertiary" />
                            {formatDate(award.date)}
                          </span>
                        </div>
                      </div>
                      <div className="bg-accent/10 text-accent text-xs font-medium px-2.5 py-1 rounded-full">
                        Achievement
                      </div>
                    </div>
                    
                    <div className="mt-4 space-y-3">
                      {award.summary.split('\n\n').map((paragraph, i) => (
                        <p key={i} className="text-text-secondary leading-relaxed">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                    
                    {award.highlights && award.highlights.length > 0 && (
                      <div className="mt-4 pt-4 border-t border-border/30">
                        <h5 className="text-sm font-medium text-primary mb-2">Key Highlights:</h5>
                        <ul className="space-y-2">
                          {award.highlights.map((highlight, i) => (
                            <li key={i} className="flex items-start">
                              <span className="text-accent mr-2 mt-1">•</span>
                              <span className="text-sm text-text-secondary">{highlight}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {/* Additional achievements from work experience */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                {[
                  {
                    title: 'Cloud Transformation',
                    description: 'Led digital transformation initiatives for multiple Fortune 500 companies, migrating legacy systems to cloud-native architectures',
                    icon: 'Cloud',
                    color: 'text-blue-400'
                  },
                  {
                    title: 'Open Source Contributions',
                    description: 'Active contributor to open-source projects including OpenStack and Kubernetes ecosystems',
                    icon: 'GitMerge',
                    color: 'text-purple-400'
                  },
                  {
                    title: 'Thought Leadership',
                    description: 'Featured speaker at major industry conferences including AWS re:Invent and Red Hat Summit',
                    icon: 'Mic',
                    color: 'text-amber-400'
                  },
                  {
                    title: 'Mentorship',
                    description: 'Mentored dozens of engineers and architects in cloud technologies and best practices',
                    icon: 'Users',
                    color: 'text-emerald-400'
                  }
                ].map((item, index) => (
                  <div key={index} className="bg-background/50 p-5 rounded-lg border border-border/50 hover:border-accent/30 transition-colors">
                    <div className={`w-10 h-10 rounded-full ${item.color.replace('text-', 'bg-')}/20 flex items-center justify-center mb-3`}>
                      <Icon name={item.icon} size={18} className={item.color} />
                    </div>
                    <h4 className="font-semibold text-primary mb-1">{item.title}</h4>
                    <p className="text-sm text-text-secondary">{item.description}</p>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-background/50 border border-border rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon name="Award" size={24} className="text-text-tertiary" />
              </div>
              <h4 className="text-lg font-medium text-primary mb-1">No awards found</h4>
              <p className="text-text-secondary max-w-md mx-auto">
                Recognized for technical excellence and innovation in cloud computing and enterprise solutions.
              </p>
            </div>
          )}
        </div>
      )
    },
    {
      id: 'publications',
      title: 'Publications & Media',
      icon: 'Book',
      content: (
        <div className="space-y-4">
          {publications.length > 0 ? (
            <div className="space-y-6">
              {publications.map((pub, index) => (
                <div key={index} className="border-b border-border/50 pb-4 last:border-0 last:pb-0">
                  <a 
                    href={pub.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-accent hover:underline font-medium"
                  >
                    "{pub.name}"
                  </a>
                  <p className="text-sm text-text-secondary mt-1">
                    {pub.publisher} • {formatDate(pub.releaseDate)}
                  </p>
                  {pub.summary && (
                    <p className="mt-2 text-text-secondary text-sm">
                      {pub.summary.split('\n').map((paragraph, i) => (
                        <p key={i} className="mb-2">{paragraph}</p>
                      ))}
                    </p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-text-secondary">Featured in various technology publications and industry media.</p>
          )}
        </div>
      )
    }
  ];

  const toggleSection = (sectionId) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  return (
    <section id="biography" className="py-16 lg:py-20 bg-background">
      <div className="max-w-4xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            My Story
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            Get to know the person behind the code - my background, philosophy, achievements, and publications.
          </p>
        </div>

        <div className="space-y-6">
          {biographySections.map((section) => (
            <div
              key={section.id}
              className="bg-surface rounded-xl border border-border overflow-hidden transition-smooth hover:shadow-card"
            >
              <button
                onClick={() => toggleSection(section.id)}
                className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-background/50 transition-smooth"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
                    <Icon name={section.icon} size={20} className="text-accent" />
                  </div>
                  <h3 className="text-lg font-semibold text-primary">
                    {section.title}
                  </h3>
                </div>
                <Icon 
                  name="ChevronDown" 
                  size={20} 
                  className={`text-text-secondary transition-transform duration-200 ${
                    expandedSection === section.id ? 'rotate-180' : ''
                  }`}
                />
              </button>
              
              {expandedSection === section.id && (
                <div className="px-6 pb-6">
                  <div className="pl-14">
                    {typeof section.content === 'string' ? (
                    section.content.split('\n\n').map((paragraph, index) => (
                      <p key={index} className="text-text-secondary leading-relaxed mb-4 last:mb-0">
                        {paragraph}
                      </p>
                    ))
                  ) : (
                    section.content
                  )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Core Values */}
        <div className="mt-16">
          <h3 className="text-xl font-semibold text-primary mb-8 text-center">
            Core Values & Principles
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: 'Target',
                title: 'Technical Excellence',
                description: 'Delivering high-quality, scalable solutions using cutting-edge cloud-native technologies and best practices.'
              },
              {
                icon: 'Users',
                title: 'Client-Centric',
                description: 'Aligning technology solutions with business objectives to drive measurable results and value.'
              },
              {
                icon: 'TrendingUp',
                title: 'Innovation',
                description: 'Leveraging emerging technologies like AI/ML to solve complex challenges and drive digital transformation.'
              },
              {
                icon: 'GitMerge',
                title: 'Open Source',
                description: 'Contributing to and leveraging open-source technologies to build flexible, future-proof solutions.'
              },
              {
                icon: 'MessageSquare',
                title: 'Knowledge Sharing',
                description: 'Mentoring teams, publishing insights, and speaking at industry events to share expertise.'
              },
              {
                icon: 'Heart',
                title: 'Community',
                description: volunteerWork.length > 0 
                  ? `Volunteering with ${volunteerWork[0].organization} to support community initiatives.`
                  : 'Committed to giving back to the technology and local communities.'
              },
              {
                icon: 'Shield',
                title: 'Security & Compliance',
                description: 'Designing solutions with security and compliance as foundational principles.'
              },
              {
                icon: 'GitBranch',
                title: 'DevOps & Automation',
                description: 'Implementing CI/CD and automation to accelerate delivery and improve reliability.'
              }
            ].map((value, index) => (
              <div key={index} className="text-left p-6 bg-surface rounded-xl border border-border hover:shadow-md transition-shadow">
                <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                  <Icon name={value.icon} size={20} className="text-accent" />
                </div>
                <h4 className="font-semibold text-primary mb-2">{value.title}</h4>
                <p className="text-sm text-text-secondary">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BiographySection;