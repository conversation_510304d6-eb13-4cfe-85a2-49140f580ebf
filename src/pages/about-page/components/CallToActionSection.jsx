import React, { useState } from 'react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Icon from '../../../components/AppIcon';

const CallToActionSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Mock form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitStatus('success');
      setFormData({ name: '', email: '', message: '' });
      
      setTimeout(() => {
        setSubmitStatus(null);
      }, 3000);
    }, 1500);
  };

  const handleDownloadResume = () => {
    // Mock resume download
    const link = document.createElement('a');
    link.href = '#';
    link.download = 'john-doe-resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const socialLinks = [
    { name: 'LinkedIn', icon: 'Linkedin', url: '#', color: 'text-blue-600' },
    { name: 'GitHub', icon: 'Github', url: '#', color: 'text-gray-800' },
    { name: 'Twitter', icon: 'Twitter', url: '#', color: 'text-blue-400' },
    { name: 'Email', icon: 'Mail', url: 'mailto:<EMAIL>', color: 'text-red-500' }
  ];

  return (
    <section id="cta" className="py-16 lg:py-20 bg-gradient-to-br from-accent/5 to-primary/5">
      <div className="max-w-6xl mx-auto px-6 lg:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Left Column - Contact Form */}
          <div>
            <div className="mb-8">
              <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
                Let's Work Together
              </h2>
              <p className="text-lg text-text-secondary">
                Ready to bring your ideas to life? I'd love to hear about your project and discuss how we can collaborate.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Input
                  type="text"
                  name="name"
                  placeholder="Your Name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full"
                />
                <Input
                  type="email"
                  name="email"
                  placeholder="Your Email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full"
                />
              </div>
              
              <textarea
                name="message"
                placeholder="Tell me about your project..."
                value={formData.message}
                onChange={handleInputChange}
                required
                rows={4}
                className="w-full px-4 py-3 border border-border rounded-lg bg-background text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent resize-none"
              />

              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                iconName="Send"
                iconPosition="right"
                fullWidth
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </Button>

              {submitStatus === 'success' && (
                <div className="flex items-center space-x-2 text-success bg-success/10 p-4 rounded-lg">
                  <Icon name="CheckCircle" size={20} />
                  <span>Message sent successfully! I'll get back to you soon.</span>
                </div>
              )}
            </form>
          </div>

          {/* Right Column - Contact Info & Actions */}
          <div className="space-y-8">
            {/* Download Resume */}
            <div className="bg-background rounded-2xl p-8 border border-border shadow-card">
              <div className="text-center">
                <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Icon name="FileText" size={24} className="text-accent" />
                </div>
                <h3 className="text-xl font-semibold text-primary mb-2">
                  Download My Resume
                </h3>
                <p className="text-text-secondary mb-6">
                  Get a detailed overview of my experience, skills, and achievements.
                </p>
                <Button
                  variant="outline"
                  iconName="Download"
                  iconPosition="left"
                  onClick={handleDownloadResume}
                  fullWidth
                >
                  Download PDF Resume
                </Button>
              </div>
            </div>

            {/* Social Links */}
            <div className="bg-background rounded-2xl p-8 border border-border shadow-card">
              <h3 className="text-xl font-semibold text-primary mb-6 text-center">
                Connect With Me
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-3 p-4 bg-surface rounded-lg border border-border hover:shadow-card transition-smooth group"
                  >
                    <Icon 
                      name={social.icon} 
                      size={20} 
                      className={`${social.color} group-hover:scale-110 transition-transform`}
                    />
                    <span className="font-medium text-text-primary group-hover:text-accent transition-smooth">
                      {social.name}
                    </span>
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-background rounded-2xl p-8 border border-border shadow-card">
              <h3 className="text-xl font-semibold text-primary mb-6 text-center">
                Why Work With Me?
              </h3>
              <div className="space-y-4">
                {[
                  { icon: 'Clock', label: 'Fast Turnaround', value: '24-48h Response' },
                  { icon: 'Award', label: 'Quality Assured', value: '100% Satisfaction' },
                  { icon: 'Users', label: 'Collaborative', value: 'Clear Communication' },
                  { icon: 'Zap', label: 'Modern Tech', value: 'Latest Technologies' }
                ].map((item, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center">
                      <Icon name={item.icon} size={16} className="text-accent" />
                    </div>
                    <div>
                      <div className="font-medium text-primary">{item.label}</div>
                      <div className="text-sm text-text-secondary">{item.value}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToActionSection;