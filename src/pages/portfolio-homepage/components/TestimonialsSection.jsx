import React, { useState, useEffect } from 'react';
import Image from '../../../components/AppImage';
import Icon from '../../../components/AppIcon';

const TestimonialsSection = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'Product Manager',
      company: 'TechCorp Solutions',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      content: `<PERSON> delivered exceptional work on our e-commerce platform. His attention to detail and ability to translate complex requirements into elegant solutions impressed our entire team. The project was completed ahead of schedule and exceeded our expectations.`,
      rating: 5,
      project: 'E-Commerce Platform'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'CTO',
      company: 'StartupHub Inc.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      content: `Working with <PERSON> was a game-changer for our startup. He not only built our MVP but also provided valuable insights on scalability and user experience. His technical expertise and communication skills are top-notch.`,
      rating: 5,
      project: 'Task Management App'
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Design Director',
      company: 'Creative Agency Pro',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      content: `<PERSON> has an incredible ability to bring designs to life with pixel-perfect precision. His collaboration with our design team was seamless, and he always found creative solutions to technical challenges. Highly recommended!`,
      rating: 5,
      project: 'Weather Dashboard'
    },
    {
      id: 4,
      name: 'David Thompson',
      role: 'Lead Developer',
      company: 'Enterprise Solutions Ltd.',
      avatar: 'https://randomuser.me/api/portraits/men/56.jpg',
      content: `Alex's backend development skills are outstanding. He built a robust API that handles our complex business logic flawlessly. His code quality and documentation made it easy for our team to maintain and extend the system.`,
      rating: 5,
      project: 'Social Media API'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index) => {
    setCurrentTestimonial(index);
  };

  return (
    <section id="testimonials" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-16">
          <span className="text-accent font-semibold text-sm uppercase tracking-wide">Client Testimonials</span>
          <h2 className="text-3xl md:text-4xl font-bold text-primary mt-2 mb-4">
            What Clients Say About My Work
          </h2>
          <p className="text-text-secondary text-lg max-w-2xl mx-auto">
            Real feedback from satisfied clients who trusted me with their projects
          </p>
        </div>
        
        <div className="relative max-w-4xl mx-auto">
          {/* Main Testimonial */}
          <div className="bg-surface rounded-lg p-8 md:p-12 card-shadow relative overflow-hidden">
            {/* Quote Icon */}
            <div className="absolute top-6 right-6 text-accent/20">
              <Icon name="Quote" size={48} />
            </div>
            
            <div className="relative z-10">
              {/* Rating */}
              <div className="flex items-center mb-6">
                {[...Array(testimonials[currentTestimonial].rating)].map((_, index) => (
                  <Icon key={index} name="Star" size={20} className="text-warning fill-current" />
                ))}
                <span className="ml-2 text-text-secondary text-sm">
                  {testimonials[currentTestimonial].rating}.0
                </span>
              </div>
              
              {/* Content */}
              <blockquote className="text-lg md:text-xl text-text-primary leading-relaxed mb-8 italic">
                "{testimonials[currentTestimonial].content}"
              </blockquote>
              
              {/* Author Info */}
              <div className="flex items-center">
                <div className="w-16 h-16 rounded-full overflow-hidden mr-4">
                  <Image
                    src={testimonials[currentTestimonial].avatar}
                    alt={testimonials[currentTestimonial].name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-semibold text-primary text-lg">
                    {testimonials[currentTestimonial].name}
                  </div>
                  <div className="text-text-secondary">
                    {testimonials[currentTestimonial].role} at {testimonials[currentTestimonial].company}
                  </div>
                  <div className="text-accent text-sm mt-1">
                    Project: {testimonials[currentTestimonial].project}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Navigation Controls */}
          <div className="flex items-center justify-between mt-8">
            {/* Previous Button */}
            <button
              onClick={prevTestimonial}
              className="w-12 h-12 bg-surface hover:bg-accent/10 rounded-full flex items-center justify-center text-text-secondary hover:text-accent transition-smooth"
              aria-label="Previous testimonial"
            >
              <Icon name="ChevronLeft" size={20} />
            </button>
            
            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-smooth ${
                    index === currentTestimonial
                      ? 'bg-accent' :'bg-border hover:bg-accent/50'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
            
            {/* Next Button */}
            <button
              onClick={nextTestimonial}
              className="w-12 h-12 bg-surface hover:bg-accent/10 rounded-full flex items-center justify-center text-text-secondary hover:text-accent transition-smooth"
              aria-label="Next testimonial"
            >
              <Icon name="ChevronRight" size={20} />
            </button>
          </div>
        </div>
        
        {/* All Testimonials Preview */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              onClick={() => goToTestimonial(index)}
              className={`bg-surface rounded-lg p-4 cursor-pointer transition-smooth ${
                index === currentTestimonial
                  ? 'ring-2 ring-accent bg-accent/5' :'hover:bg-background'
              }`}
            >
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-medium text-primary text-sm">{testimonial.name}</div>
                  <div className="text-text-secondary text-xs">{testimonial.company}</div>
                </div>
              </div>
              <p className="text-text-secondary text-sm line-clamp-3">
                "{testimonial.content.substring(0, 100)}..."
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;