import React from 'react';
import { Link } from 'react-router-dom';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const FeaturedProjects = () => {
  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      category: 'Full Stack',
      liveUrl: 'https://demo-ecommerce.com',
      githubUrl: 'https://github.com/alexjohnson/ecommerce'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop',
      technologies: ['React', 'Firebase', 'Material-UI', 'Socket.io'],
      category: 'Frontend',
      liveUrl: 'https://taskmanager-demo.com',
      githubUrl: 'https://github.com/alexjohnson/taskmanager'
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'A responsive weather dashboard with location-based forecasts, interactive maps, and detailed weather analytics using multiple APIs.',
      image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=600&h=400&fit=crop',
      technologies: ['Vue.js', 'Chart.js', 'OpenWeather API', 'Tailwind'],
      category: 'Frontend',
      liveUrl: 'https://weather-dashboard-demo.com',
      githubUrl: 'https://github.com/alexjohnson/weather-dashboard'
    },
    {
      id: 4,
      title: 'Social Media API',
      description: 'RESTful API for a social media platform with user management, post creation, real-time messaging, and comprehensive documentation.',
      image: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=600&h=400&fit=crop',
      technologies: ['Node.js', 'Express', 'PostgreSQL', 'JWT'],
      category: 'Backend',
      liveUrl: 'https://api-docs-demo.com',
      githubUrl: 'https://github.com/alexjohnson/social-api'
    }
  ];

  return (
    <section id="featured-projects" className="py-20 bg-surface">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-16">
          <span className="text-accent font-semibold text-sm uppercase tracking-wide">Featured Work</span>
          <h2 className="text-3xl md:text-4xl font-bold text-primary mt-2 mb-4">
            Projects That Define My Expertise
          </h2>
          <p className="text-text-secondary text-lg max-w-2xl mx-auto">
            A showcase of my best work, demonstrating technical skills and creative problem-solving
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {projects.map((project, index) => (
            <div 
              key={project.id}
              className="bg-background rounded-lg overflow-hidden card-shadow hover:shadow-interactive transition-smooth group"
            >
              <div className="relative overflow-hidden">
                <Image
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-accent text-accent-foreground text-xs font-medium rounded-full">
                    {project.category}
                  </span>
                </div>
                <div className="absolute top-4 right-4 flex space-x-2">
                  <a
                    href={project.liveUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-8 h-8 bg-background/90 backdrop-blur-sm rounded-full flex items-center justify-center text-text-primary hover:bg-accent hover:text-accent-foreground transition-smooth"
                    aria-label="View live demo"
                  >
                    <Icon name="ExternalLink" size={14} />
                  </a>
                  <a
                    href={project.githubUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-8 h-8 bg-background/90 backdrop-blur-sm rounded-full flex items-center justify-center text-text-primary hover:bg-accent hover:text-accent-foreground transition-smooth"
                    aria-label="View source code"
                  >
                    <Icon name="Github" size={14} />
                  </a>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-semibold text-primary mb-3 group-hover:text-accent transition-smooth">
                  {project.title}
                </h3>
                
                <p className="text-text-secondary mb-4 leading-relaxed">
                  {project.description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.map((tech, techIndex) => (
                    <span 
                      key={techIndex}
                      className="px-3 py-1 bg-surface text-text-secondary text-xs rounded-full border border-border"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center justify-between">
                  <Link to="/project-detail-page" state={{ project }}>
                    <Button 
                      variant="ghost"
                      size="sm"
                      iconName="ArrowRight"
                      iconPosition="right"
                    >
                      View Details
                    </Button>
                  </Link>
                  
                  <div className="flex space-x-2">
                    <a
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-text-secondary hover:text-accent transition-smooth"
                      aria-label="Live demo"
                    >
                      <Icon name="Globe" size={18} />
                    </a>
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-text-secondary hover:text-accent transition-smooth"
                      aria-label="Source code"
                    >
                      <Icon name="Github" size={18} />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/projects-portfolio">
            <Button 
              variant="primary"
              size="lg"
              iconName="FolderOpen"
              iconPosition="right"
            >
              View All Projects
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjects;