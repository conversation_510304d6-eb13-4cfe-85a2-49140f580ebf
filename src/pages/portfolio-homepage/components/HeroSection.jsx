import React from 'react';
import { Link } from 'react-router-dom';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const HeroSection = () => {
  return (
    <section id="hero" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-surface to-background relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-accent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-primary rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-6 lg:px-12 py-20 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Content */}
          <div className="text-center lg:text-left order-2 lg:order-1">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary mb-6 leading-tight">
              <span className="text-accent">Nicholas Gerasimatos</span>
              <br />
              <span className="text-text-secondary text-3xl md:text-4xl lg:text-5xl">
                Principal Technical Consultant
              </span>
            </h1>
            
            <p className="text-lg md:text-xl text-text-secondary mb-8 max-w-2xl">
              I craft exceptional digital experiences through innovative web applications. 
              Specializing in React, Node.js, and modern web technologies to bring ideas to life.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link to="/projects-portfolio">
                <Button 
                  variant="primary" 
                  size="lg"
                  iconName="FolderOpen"
                  iconPosition="right"
                  className="w-full sm:w-auto"
                >
                  View My Work
                </Button>
              </Link>
              
              <Link to="/contact-page">
                <Button 
                  variant="outline" 
                  size="lg"
                  iconName="Mail"
                  iconPosition="right"
                  className="w-full sm:w-auto"
                >
                  Get In Touch
                </Button>
              </Link>
            </div>
            
            {/* Social Links */}
            <div className="flex items-center justify-center lg:justify-start space-x-4 mt-8">
              <span className="text-text-secondary text-sm">Follow me:</span>
              <div className="flex space-x-3">
                {[
                  { name: 'Github', icon: 'Github', url: 'https://github.com' },
                  { name: 'Linkedin', icon: 'Linkedin', url: 'https://linkedin.com' },
                  { name: 'Twitter', icon: 'Twitter', url: 'https://twitter.com' },
                  { name: 'Instagram', icon: 'Instagram', url: 'https://instagram.com' }
                ].map((social) => (
                  <a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-surface hover:bg-accent/10 rounded-full flex items-center justify-center text-text-secondary hover:text-accent transition-smooth"
                    aria-label={`Follow on ${social.name}`}
                  >
                    <Icon name={social.icon} size={18} />
                  </a>
                ))}
              </div>
            </div>
          </div>
          
          {/* Profile Image */}
          <div className="order-1 lg:order-2 flex justify-center">
            <div className="relative">
              <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden shadow-interactive bg-surface">
                <Image
                  src="/SCR-20250626-jqdc.png"
                  alt="Nicholas Gerasimatos - Full Stack Developer"
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-accent/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <Icon name="Code" size={24} className="text-accent" />
              </div>
              
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-success/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <Icon name="Zap" size={20} className="text-success" />
              </div>
            </div>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="flex flex-col items-center text-text-secondary">
            <span className="text-sm mb-2">Scroll to explore</span>
            <Icon name="ChevronDown" size={20} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;