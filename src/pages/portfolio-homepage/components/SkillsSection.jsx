import React from 'react';
import Icon from '../../../components/AppIcon';

const SkillsSection = () => {
  const skillCategories = [
    {
      title: 'Cloud & Infrastructure',
      icon: 'Cloud',
      skills: [
        { name: 'AWS', level: 95, icon: 'Cloud' },
        { name: 'OpenShift', level: 90, icon: 'Server' },
        { name: 'Kubernetes', level: 92, icon: 'Container' },
        { name: 'Docker', level: 90, icon: 'Package' },
        { name: 'Terraform', level: 88, icon: 'LayoutGrid' },
        { name: 'Ansible', level: 85, icon: 'Terminal' }
      ]
    },
    {
      title: 'AI/ML & Data',
      icon: 'Brain',
      skills: [
        { name: 'AI/ML Integration', level: 95, icon: 'Brain' },
        { name: 'Large Language Models (LLM)', level: 92, icon: 'MessageSquare' },
        { name: 'Machine Learning', level: 90, icon: 'Cpu' },
        { name: 'RAG Systems', level: 88, icon: 'Database' },
        { name: 'AWS Bedrock', level: 90, icon: 'Cloud' },
        { name: 'Jupyter Notebooks', level: 92, icon: 'FileCode' },
        { name: 'AI Agent Frameworks', level: 85, icon: 'Bot' },
        { name: 'MCP Integration', level: 88, icon: 'Share2' },
        { name: 'TensorFlow/PyTorch', level: 85, icon: 'BrainCircuit' },
        { name: 'Python', level: 95, icon: 'Code2' },
        { name: 'Data Analytics', level: 90, icon: 'BarChart' }
      ]
    },
    {
      title: 'Enterprise Solutions',
      icon: 'Building2',
      skills: [
        { name: 'Digital Transformation', level: 95, icon: 'RefreshCw' },
        { name: 'Cloud Migration', level: 92, icon: 'UploadCloud' },
        { name: 'DevSecOps', level: 90, icon: 'Shield' },
        { name: 'Microservices', level: 88, icon: 'Puzzle' },
        { name: 'CI/CD Pipelines', level: 90, icon: 'GitBranch' }
      ]
    },
    {
      title: 'Leadership & Strategy',
      icon: 'Users',
      skills: [
        { name: 'Technical Leadership', level: 95, icon: 'Award' },
        { name: 'Cloud Strategy', level: 92, icon: 'Map' },
        { name: 'Stakeholder Management', level: 90, icon: 'Handshake' },
        { name: 'Team Building', level: 88, icon: 'Users' },
        { name: 'Mentorship', level: 90, icon: 'GraduationCap' }
      ]
    }
  ];

  return (
    <section id="skills" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-16">
          <span className="text-accent font-semibold text-sm uppercase tracking-wide">Skills & Expertise</span>
          <h2 className="text-3xl md:text-4xl font-bold text-primary mt-2 mb-4">
            What I Bring to the Table
          </h2>
          <p className="text-text-secondary text-lg max-w-2xl mx-auto">
            A comprehensive skill set built through years of hands-on experience and continuous learning
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <div 
              key={categoryIndex}
              className="bg-surface rounded-lg p-6 card-shadow hover:shadow-interactive transition-smooth"
            >
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center mr-3">
                  <Icon name={category.icon} size={20} className="text-accent" />
                </div>
                <h3 className="text-lg font-semibold text-primary">{category.title}</h3>
              </div>
              
              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className="group flex items-center py-1">
                    <Icon name={skill.icon} size={16} className="text-text-secondary mr-2 flex-shrink-0" />
                    <span className="text-text-primary font-medium text-sm">{skill.name}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        {/* Additional Skills Tags */}
        <div className="mt-16 text-center">
          <h3 className="text-xl font-semibold text-primary mb-6">Additional Technologies</h3>
          <div className="flex flex-wrap justify-center gap-3">
            {[
            'Multi-Cloud', 'OpenStack', 'Serverless', 'GenAI', 'MLOps',
            'Cloud Native', 'IaC', 'GitOps', 'Agile', 'Scrum',
            'Enterprise Architecture', 'Solution Design', 'ROSA', 'AI/ML',
            'Cloud Security', 'Compliance', 'Cost Optimization', 'Performance Tuning'
          ].map((tech, index) => (
              <span 
                key={index}
                className="px-4 py-2 bg-surface text-text-secondary rounded-full text-sm hover:bg-accent/10 hover:text-accent transition-smooth cursor-default"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;