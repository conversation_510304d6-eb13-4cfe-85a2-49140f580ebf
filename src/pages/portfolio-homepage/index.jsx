import React, { useEffect } from 'react';
import Header from '../../components/ui/Header';
import NavigationIndicator from '../../components/ui/NavigationIndicator';
import HeroSection from './components/HeroSection';
import AboutPreview from './components/AboutPreview';
import SkillsSection from './components/SkillsSection';
import FeaturedProjects from './components/FeaturedProjects';
import TestimonialsSection from './components/TestimonialsSection';
import Footer from './components/Footer';

const PortfolioHomepage = () => {
  const sections = [
    { id: 'hero', label: 'Home' },
    { id: 'about-preview', label: 'About' },
    { id: 'skills', label: 'Skills' },
    { id: 'featured-projects', label: 'Projects' },
    { id: 'testimonials', label: 'Testimonials' }
  ];

  useEffect(() => {
    // Smooth scroll behavior for anchor links
    const handleSmoothScroll = (e) => {
      const target = e.target.closest('a[href^="#"]');
      if (target) {
        e.preventDefault();
        const targetId = target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          const headerOffset = 100;
          const elementPosition = targetElement.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }
    };

    document.addEventListener('click', handleSmoothScroll);
    return () => document.removeEventListener('click', handleSmoothScroll);
  }, []);

  useEffect(() => {
    // Set page title and meta description
    document.title = 'Nicholas Gerasimatos - Full Stack Developer | Nicholas Gerasimatos';
    
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 
        'Full Stack Developer specializing in React, Node.js, and modern web technologies. View my portfolio of innovative web applications and get in touch for your next project.'
      );
    }
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <NavigationIndicator sections={sections} />
      
      <main>
        <HeroSection />
        <AboutPreview />
        <SkillsSection />
        <FeaturedProjects />
        <TestimonialsSection />
      </main>
      
      <Footer />
    </div>
  );
};

export default PortfolioHomepage;