import React from 'react';
import Input from '../../../components/ui/Input';
import Icon from '../../../components/AppIcon';

const SearchAndSort = ({ 
  searchQuery, 
  onSearchChange, 
  sortBy, 
  onSortChange, 
  resultsCount 
}) => {
  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'alphabetical', label: 'A-Z' },
    { value: 'popularity', label: 'Most Popular' }
  ];

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-6">
      {/* Search Input */}
      <div className="relative flex-1 max-w-md">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Icon name="Search" size={20} className="text-text-secondary" />
        </div>
        <Input
          type="search"
          placeholder="Search projects..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Sort and Results */}
      <div className="flex items-center gap-4 w-full sm:w-auto">
        {/* Results Count */}
        <span className="text-sm text-text-secondary whitespace-nowrap">
          {resultsCount} {resultsCount === 1 ? 'project' : 'projects'}
        </span>

        {/* Sort Dropdown */}
        <div className="relative">
          <select
            value={sortBy}
            onChange={(e) => onSortChange(e.target.value)}
            className="appearance-none bg-background border border-border rounded-md px-4 py-2 pr-8 text-sm text-text-primary focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-smooth"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <Icon name="ChevronDown" size={16} className="text-text-secondary" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchAndSort;