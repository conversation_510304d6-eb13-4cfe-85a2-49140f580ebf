import React from 'react';
import { Link } from 'react-router-dom';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const ProjectCard = ({ project, onPreview }) => {
  const handlePreview = (e) => {
    e.preventDefault();
    if (onPreview) {
      onPreview(project);
    }
  };

  return (
    <div 
      className="bg-background rounded-lg card-shadow border border-border overflow-hidden group hover:shadow-interactive transition-layout"
      onMouseEnter={handlePreview}
    >
      {/* Project Image */}
      <div className="relative overflow-hidden h-48 sm:h-56">
        <Image
          src={project.image}
          alt={project.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-layout"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-smooth" />
        
        {/* Live Demo Badge */}
        {project.liveUrl && (
          <div className="absolute top-3 right-3">
            <span className="bg-success text-success-foreground text-xs px-2 py-1 rounded-full font-medium">
              Live
            </span>
          </div>
        )}
      </div>

      {/* Project Content */}
      <div className="p-4 sm:p-6">
        {/* Project Title */}
        <h3 className="text-lg sm:text-xl font-semibold text-text-primary mb-2 group-hover:text-accent transition-smooth">
          {project.title}
        </h3>

        {/* Project Description */}
        <p className="text-text-secondary text-sm sm:text-base mb-4 line-clamp-3">
          {project.description}
        </p>

        {/* Technology Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {project.technologies.slice(0, 3).map((tech, index) => (
            <span
              key={index}
              className="bg-surface text-text-secondary text-xs px-2 py-1 rounded-md font-medium"
            >
              {tech}
            </span>
          ))}
          {project.technologies.length > 3 && (
            <span className="bg-surface text-text-secondary text-xs px-2 py-1 rounded-md font-medium">
              +{project.technologies.length - 3}
            </span>
          )}
        </div>

        {/* Project Meta */}
        <div className="flex items-center justify-between text-xs text-text-secondary mb-4">
          <div className="flex items-center space-x-4">
            <span className="flex items-center space-x-1">
              <Icon name="Calendar" size={14} />
              <span>{project.year}</span>
            </span>
            <span className="flex items-center space-x-1">
              <Icon name="Clock" size={14} />
              <span>{project.duration}</span>
            </span>
          </div>
          <span className="bg-accent/10 text-accent px-2 py-1 rounded-md font-medium">
            {project.category}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2">
          <Link to="/project-detail-page" className="flex-1">
            <Button 
              variant="primary" 
              fullWidth
              iconName="Eye"
              iconPosition="left"
            >
              View Details
            </Button>
          </Link>
          {project.liveUrl && (
            <Button 
              variant="outline" 
              fullWidth
              iconName="ExternalLink"
              iconPosition="right"
              onClick={() => window.open(project.liveUrl, '_blank')}
            >
              Live Demo
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;