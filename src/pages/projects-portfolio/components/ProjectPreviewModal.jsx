import React from 'react';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const ProjectPreviewModal = ({ project, isOpen, onClose }) => {
  if (!isOpen || !project) return null;

  return (
    <div className="fixed inset-0 z-300 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-primary/40 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative bg-background rounded-xl shadow-interactive max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 bg-background/80 backdrop-blur-sm rounded-full text-text-secondary hover:text-text-primary transition-smooth"
          aria-label="Close preview"
        >
          <Icon name="X" size={20} />
        </button>

        {/* Project Image */}
        <div className="relative h-64 overflow-hidden rounded-t-xl">
          <Image
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent" />
        </div>

        {/* Project Content */}
        <div className="p-6">
          {/* Title and Category */}
          <div className="flex items-start justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-2">
                {project.title}
              </h2>
              <span className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm font-medium">
                {project.category}
              </span>
            </div>
          </div>

          {/* Description */}
          <p className="text-text-secondary mb-6 leading-relaxed">
            {project.fullDescription || project.description}
          </p>

          {/* Technologies */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-text-primary mb-3">Technologies Used</h3>
            <div className="flex flex-wrap gap-2">
              {project.technologies.map((tech, index) => (
                <span
                  key={index}
                  className="bg-surface text-text-secondary text-sm px-3 py-1 rounded-md font-medium"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>

          {/* Project Details */}
          <div className="grid grid-cols-2 gap-4 mb-6 p-4 bg-surface rounded-lg">
            <div>
              <span className="text-xs text-text-secondary uppercase tracking-wide">Year</span>
              <p className="text-sm font-medium text-text-primary">{project.year}</p>
            </div>
            <div>
              <span className="text-xs text-text-secondary uppercase tracking-wide">Duration</span>
              <p className="text-sm font-medium text-text-primary">{project.duration}</p>
            </div>
            <div>
              <span className="text-xs text-text-secondary uppercase tracking-wide">Role</span>
              <p className="text-sm font-medium text-text-primary">{project.role || 'Full Stack Developer'}</p>
            </div>
            <div>
              <span className="text-xs text-text-secondary uppercase tracking-wide">Team Size</span>
              <p className="text-sm font-medium text-text-primary">{project.teamSize || 'Solo Project'}</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              variant="primary" 
              fullWidth
              iconName="Eye"
              iconPosition="left"
              onClick={onClose}
            >
              View Full Details
            </Button>
            {project.liveUrl && (
              <Button 
                variant="outline" 
                fullWidth
                iconName="ExternalLink"
                iconPosition="right"
                onClick={() => window.open(project.liveUrl, '_blank')}
              >
                Live Demo
              </Button>
            )}
            {project.githubUrl && (
              <Button 
                variant="ghost" 
                fullWidth
                iconName="Github"
                iconPosition="left"
                onClick={() => window.open(project.githubUrl, '_blank')}
              >
                Source Code
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectPreviewModal;