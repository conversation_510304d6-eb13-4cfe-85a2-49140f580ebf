import React from 'react';
import ProjectCard from './ProjectCard';

const ProjectGrid = ({ projects, onProjectPreview, isLoading }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="bg-surface rounded-lg overflow-hidden animate-pulse">
            <div className="h-48 sm:h-56 bg-text-secondary/20" />
            <div className="p-4 sm:p-6">
              <div className="h-6 bg-text-secondary/20 rounded mb-2" />
              <div className="h-4 bg-text-secondary/20 rounded mb-4" />
              <div className="flex gap-2 mb-4">
                <div className="h-6 w-16 bg-text-secondary/20 rounded" />
                <div className="h-6 w-20 bg-text-secondary/20 rounded" />
              </div>
              <div className="flex gap-2">
                <div className="h-10 flex-1 bg-text-secondary/20 rounded" />
                <div className="h-10 flex-1 bg-text-secondary/20 rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 mx-auto mb-4 bg-surface rounded-full flex items-center justify-center">
          <svg 
            className="w-12 h-12 text-text-secondary" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1.5} 
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" 
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-text-primary mb-2">No Projects Found</h3>
        <p className="text-text-secondary">
          Try adjusting your search criteria or filters to find more projects.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          onPreview={onProjectPreview}
        />
      ))}
    </div>
  );
};

export default ProjectGrid;