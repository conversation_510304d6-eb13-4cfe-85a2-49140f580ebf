import React, { useState } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const FilterSection = ({ 
  categories, 
  technologies, 
  selectedCategory, 
  selectedTechnologies, 
  onCategoryChange, 
  onTechnologyChange, 
  onClearFilters,
  activeFiltersCount 
}) => {
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  const toggleMobileFilter = () => {
    setIsMobileFilterOpen(!isMobileFilterOpen);
  };

  const handleTechnologyToggle = (tech) => {
    const updatedTechs = selectedTechnologies.includes(tech)
      ? selectedTechnologies.filter(t => t !== tech)
      : [...selectedTechnologies, tech];
    onTechnologyChange(updatedTechs);
  };

  const FilterContent = () => (
    <>
      {/* Category Filters */}
      <div className="mb-6">
        <h3 className="text-sm font-semibold text-text-primary mb-3">Categories</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onCategoryChange('all')}
            className={`px-3 py-2 rounded-md text-sm font-medium transition-smooth ${
              selectedCategory === 'all' ?'bg-accent text-accent-foreground' :'bg-surface text-text-secondary hover:bg-accent/10 hover:text-accent'
            }`}
          >
            All Projects
          </button>
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => onCategoryChange(category)}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-smooth ${
                selectedCategory === category
                  ? 'bg-accent text-accent-foreground'
                  : 'bg-surface text-text-secondary hover:bg-accent/10 hover:text-accent'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Technology Filters */}
      <div className="mb-6">
        <h3 className="text-sm font-semibold text-text-primary mb-3">Technologies</h3>
        <div className="flex flex-wrap gap-2">
          {technologies.map((tech) => (
            <button
              key={tech}
              onClick={() => handleTechnologyToggle(tech)}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-smooth ${
                selectedTechnologies.includes(tech)
                  ? 'bg-accent text-accent-foreground'
                  : 'bg-surface text-text-secondary hover:bg-accent/10 hover:text-accent'
              }`}
            >
              {tech}
            </button>
          ))}
        </div>
      </div>

      {/* Clear Filters */}
      {activeFiltersCount > 0 && (
        <div className="pt-4 border-t border-border">
          <Button
            variant="ghost"
            onClick={onClearFilters}
            iconName="X"
            iconPosition="left"
            fullWidth
          >
            Clear All Filters ({activeFiltersCount})
          </Button>
        </div>
      )}
    </>
  );

  return (
    <>
      {/* Desktop Filter Sidebar */}
      <div className="hidden lg:block w-64 bg-background border-r border-border p-6">
        <div className="sticky top-24">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-text-primary">Filters</h2>
            {activeFiltersCount > 0 && (
              <span className="bg-accent text-accent-foreground text-xs px-2 py-1 rounded-full">
                {activeFiltersCount}
              </span>
            )}
          </div>
          <FilterContent />
        </div>
      </div>

      {/* Mobile Filter Button */}
      <div className="lg:hidden mb-6">
        <Button
          variant="outline"
          onClick={toggleMobileFilter}
          iconName="Filter"
          iconPosition="left"
          fullWidth
        >
          Filters {activeFiltersCount > 0 && `(${activeFiltersCount})`}
        </Button>
      </div>

      {/* Mobile Filter Modal */}
      <div 
        className={`fixed inset-0 z-200 lg:hidden transition-layout ${
          isMobileFilterOpen ? 'visible' : 'invisible'
        }`}
      >
        {/* Backdrop */}
        <div 
          className={`absolute inset-0 bg-primary/20 backdrop-blur-sm transition-opacity duration-fast ${
            isMobileFilterOpen ? 'opacity-100' : 'opacity-0'
          }`}
          onClick={toggleMobileFilter}
        />
        
        {/* Filter Panel */}
        <div 
          className={`absolute bottom-0 left-0 right-0 bg-background rounded-t-xl shadow-interactive max-h-[80vh] overflow-y-auto transform transition-transform duration-normal ${
            isMobileFilterOpen ? 'translate-y-0' : 'translate-y-full'
          }`}
        >
          <div className="flex items-center justify-between p-6 border-b border-border">
            <h2 className="text-lg font-semibold text-text-primary">Filter Projects</h2>
            <button
              onClick={toggleMobileFilter}
              className="p-2 rounded-sm text-text-secondary hover:text-text-primary hover:bg-surface transition-smooth"
              aria-label="Close filters"
            >
              <Icon name="X" size={24} />
            </button>
          </div>
          
          <div className="p-6">
            <FilterContent />
          </div>
        </div>
      </div>
    </>
  );
};

export default FilterSection;