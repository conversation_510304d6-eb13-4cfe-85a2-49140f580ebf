import React from 'react';
import Icon from '../../../components/AppIcon';

const LocationMap = () => {
  // Mock coordinates for San Francisco, CA
  const latitude = 37.7749;
  const longitude = -122.4194;
  
  return (
    <div className="bg-background rounded-xl shadow-card overflow-hidden">
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3 mb-2">
          <Icon name="MapPin" size={20} className="text-accent" />
          <h2 className="text-xl font-semibold text-primary">
            Location & Availability
          </h2>
        </div>
        <p className="text-text-secondary">
          Based in San Francisco, available for remote collaboration worldwide
        </p>
      </div>
      
      <div className="relative h-64 lg:h-80">
        <iframe
          width="100%"
          height="100%"
          loading="lazy"
          title="San Francisco Location"
          referrerPolicy="no-referrer-when-downgrade"
          src={`https://www.google.com/maps?q=${latitude},${longitude}&z=12&output=embed`}
          className="border-0"
        />
        
        {/* Overlay with meeting info */}
        <div className="absolute bottom-4 left-4 right-4 bg-background/95 backdrop-blur-sm rounded-lg p-4 shadow-card">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <Icon name="Calendar" size={16} className="text-accent" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-text-primary text-sm mb-1">
                In-Person Meetings
              </h3>
              <p className="text-xs text-text-secondary leading-relaxed">
                Available for local meetings in the San Francisco Bay Area. 
                Remote consultations preferred for initial discussions.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="p-6 bg-surface">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="flex items-center gap-3">
            <Icon name="Clock" size={16} className="text-text-secondary" />
            <div>
              <p className="text-sm font-medium text-text-primary">
                Time Zone
              </p>
              <p className="text-xs text-text-secondary">
                Pacific Standard Time (PST)
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Icon name="Globe" size={16} className="text-text-secondary" />
            <div>
              <p className="text-sm font-medium text-text-primary">
                Remote Work
              </p>
              <p className="text-xs text-text-secondary">
                Available globally
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationMap;