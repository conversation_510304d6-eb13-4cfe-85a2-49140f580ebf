import React from "react";
import Routes from "./Routes";
import { StagewiseToolbar } from "@stagewise/toolbar-react";
import { ReactPlugin } from "@stagewise-plugins/react";

function App() {
  return (
    <>
      {/* Stagewise Toolbar - only renders in development mode */}
      {process.env.NODE_ENV === 'development' && (
        <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
      )}
      <Routes />
    </>
  );
}

export default App;