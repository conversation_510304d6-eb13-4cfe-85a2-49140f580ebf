import React from "react";
import { BrowserRouter, Routes as RouterRout<PERSON>, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
// Add your imports here
import PortfolioHomepage from "pages/portfolio-homepage";
import ProjectsPortfolio from "pages/projects-portfolio";
import ContactPage from "pages/contact-page";
import AboutPage from "pages/about-page";
import BlogArticlesPage from "pages/blog-articles-page";
import ProjectDetailPage from "pages/project-detail-page";
import NotFound from "pages/NotFound";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your routes here */}
        <Route path="/" element={<PortfolioHomepage />} />
        <Route path="/portfolio-homepage" element={<PortfolioHomepage />} />
        <Route path="/projects-portfolio" element={<ProjectsPortfolio />} />
        <Route path="/contact-page" element={<ContactPage />} />
        <Route path="/about-page" element={<AboutPage />} />
        <Route path="/blog-articles-page" element={<BlogArticlesPage />} />
        <Route path="/project-detail-page" element={<ProjectDetailPage />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;